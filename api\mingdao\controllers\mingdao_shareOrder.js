'use strict'

const { BranchCurdRouter } = require('accel-utils')
/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#core-controllers)
 * to customize this controller
 */
const branchCurdRouter = new (class extends BranchCurdRouter {
  constructor(modelName) {
    super(modelName)
  }

  async branchFind(ctx) {
    const { classification } = ctx.query
    if (classification) delete ctx.query.classification
    return await super.branchFind(ctx)
  }

})('mingdao_shareOrder')

module.exports = {
  ...branchCurdRouter.createHandlers(),

  async setShareOrderList(ctx) {
    try {
      // const { data } = ctx.request.body
      const body = ctx.request.body
      const result = await strapi.query('mingdao_shareOrder').create(body)
      return ctx.send(result)

    } catch (error) {
      return ctx.badRequest(error)
    }
  },

  async getShareOrderList(ctx) {
    const { order_id } = ctx.query
    if (!order_id) return ctx.badRequest('PARAMETERS_ERROR', '订单ID为空')
    const result = await strapi.query('mingdao_shareOrder').findOne({ id: order_id })

    if (!result) return ctx.badRequest('PARAMETERS_ERROR', '订单不存在')
    return ctx.send(result)
  }
}
