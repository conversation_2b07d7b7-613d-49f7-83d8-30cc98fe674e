{"collectionName": "components_config_pay-wechat", "info": {"name": "PayWechat", "label": "微信支付配置", "description": "微信支付配置"}, "options": {"draftAndPublish": false, "timestamps": true}, "pluginOptions": {}, "attributes": {"merchantId": {"label": "商户号", "type": "string", "required": true, "unique": true}, "merchantName": {"label": "商户名称", "type": "string", "required": true}, "apiKey": {"label": "ApiV3密钥", "type": "string", "required": true}, "appId": {"label": "关联appId", "type": "string"}, "serial_no": {"label": "证书序列号", "type": "string", "required": true}, "pkcs12": {"label": "pkcs12证书", "model": "file", "placeholder": "请上传pkcs12格式证书(apiclient_cert.p12)", "via": "related", "plugin": "upload", "required": false, "pluginOptions": {}, "configurable": false}, "certPem": {"label": "pem证书", "model": "file", "placeholder": "请上传证书(apiclient_cert.pem)", "via": "related", "plugin": "upload", "required": false, "pluginOptions": {}, "configurable": false}, "keyPem": {"label": "key证书", "model": "file", "placeholder": "请上传key格式证书(apiclient_key.pem)", "via": "related", "plugin": "upload", "required": false, "pluginOptions": {}, "configurable": false}}}