module.exports = ({ env }) => {

  const mingdaoConfig = {
    // api报错时的webhook
    prod: { // 私有部署正式
      appKey: 'f52a3866a2b484bb',
      sign: 'ZWJlODQxY2M5OGQ2YmY5NTU0ZGUyZTIzN2Q5Mjc0OTRkMGY1YzhmMGFiODhiYjkzMGI5OTE3MzM2OGQyNTZiNA==',
      baseUrl: 'http://10.12.40.250:8880/api',
      paySuccessNoticeKey: 'NjU4ZDM0YjI2YzdhY2E2YmRiNmNiNGNi',
      refundSuccessNoticeKey: 'NjU4ZDM0YjI2YzdhY2E2YmRiNmNiNGM4',
      refundFailNoticeKey: 'NjU4ZDM0YjI2YzdhY2E2YmRiNmNiNGNj'
    },
    saas: { // 明道云saas
      appKey: '7f6cdbfc447eb56d',
      sign: 'MDkwMTdhMDQ3MjNmNGQ1ZWUzNTRkM2ZhYTUxMGQxMmNhOTc1M2NlZWQ1OWIwNzVkMWU3NzQyNDQ1YTY3YTNmYg==',
      baseUrl: 'https://api.mingdao.com',
      paySuccessNoticeKey: 'NjU4MjllN2Y0ZjJhZDUxNTk5ZGM5ZjZl',
      refundSuccessNoticeKey: 'NjU4MjllNjhjZTI1NGUwZTM0ZjVhYzJm',
      refundFailNoticeKey: 'NjU4M2VmOTc2MmY1OGY1MDIwN2U4ZTkw'
    },
    local: { // 私有部署测试
      appKey: '90f28a91e6818038',
      sign: 'MDNiN2UzYjkwODMzMjRhOWY1OWJjNDRjNWQ3NzUyOWY0OGI3Nzc5Nzk1ZTU4MGY3YzYwMjExNWNmNDc2YjgwMg==',
      baseUrl: 'http://10.11.1.222:8880/api',
      paySuccessNoticeKey: 'NjU4YjkxNGRkOWUzMWE0MmNhZGI1Y2Iy',
      refundSuccessNoticeKey: 'NjU4YjkxNGRkOWUzMWE0MmNhZGI1Y2Fm',
      refundFailNoticeKey: 'NjU4YjkxNGRkOWUzMWE0MmNhZGI1Y2I0'
    },
  }[env('MINGDAO', 'prod')]

  return {
    // ...mingdaoConfig,
    // paySuccessNoticeUrl: `${mingdaoConfig.baseUrl}/workflow/hooks/${mingdaoConfig.paySuccessNoticeKey}`,
    // refundSuccessNoticeUrl: `${mingdaoConfig.baseUrl}/workflow/hooks/${mingdaoConfig.refundSuccessNoticeKey}`,
    // refundFailNoticeUrl: `${mingdaoConfig.baseUrl}/workflow/hooks/${mingdaoConfig.refundFailNoticeKey}`,
    worksheet: {
      customer: 'kehubiao', // 客户表
      good: 'goods', // 商品表
      order: 'xsdd', // 销售订单
      sale: 'shdd', // 售后订单
    }
  }
}
