const mingdaoApi = require('../utils/mingdaoApi')
const worksheet = strapi.config.mingdao.worksheet

async function getGoodList(ctx) {
  const { pageIndex = 1, pageSize = 20, key= '' } = ctx.request.query

  const filters = [{
    'controlId': 'shelfState',
    'dataType': 36, // 关联表格
    'spliceType': 1,
    'filterType': 2, //关联过滤
    'value': 1
  }]

  if (key) {
    filters.push({
      'controlId': 'name',
      'dataType': 2,
      'spliceType': 1,
      'filterType': 1, // 包含
      'value': key
    })
  }

  let list = await mingdaoApi.getList(worksheet.good, filters, pageIndex, pageSize)
  return ctx.wrapper.succ(list)
}

async function getSkus(ctx) {
  const { goodId } = ctx.request.query
  if (!goodId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误，goodId 不能为空')
  }

  let list = await mingdaoApi.getRowRelations(worksheet.good, goodId, 'skus')

  return ctx.wrapper.succ(list)
}

module.exports = {
  getGoodList,
  getSkus
}
