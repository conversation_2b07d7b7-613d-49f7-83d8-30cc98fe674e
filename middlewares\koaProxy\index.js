const k2c = require('koa2-connect');
const { createProxyMiddleware, responseInterceptor } = require('http-proxy-middleware');
const proxyOptions = strapi.config.get('server.proxyOptions')
module.exports = strapi => {
    return {
        initialize() {
            strapi.app.use(async (ctx, next) => {
                for (const proxyOption of Object.keys(proxyOptions)) {
                    if (ctx.url.startsWith(proxyOption)) {
                        return await k2c(createProxyMiddleware(proxyOptions[proxyOption]))(ctx, next);
                    }
                }
                return await next();
            });
        }
    }
}