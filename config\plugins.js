module.exports = ({ env }) => {
  const bosConfig = {
    prod: {
      AK: 'ALTAKOVxs9pURgBgK3dBp8LAhG',
      SK: '46f036aa332447f7801008cec77fc107',
      Bucket: 'ayx-wly',
      Endpoint: 'https://ayx-wly.bj.bcebos.com',
      customHost: 'https://wly-oss.yunxiao.com',
      baseDir: 'prod/mmd/',
      uploadPath: 'upload/',
      richTextUploadPath: 'rich-upload/',
    },
    test: {
      AK: 'ALTAK3fAE78I9f5oz2IHlhVU7q',
      SK: 'a726c9dc79834a8f8e0494bf26521119',
      Bucket: 'ayx-wly',
      Endpoint: 'https://ayx-wly.bj.bcebos.com',
      customHost: 'https://wly-oss.yunxiao.com',
      baseDir: 'test/mmd/',
      uploadPath: 'upload/',
      richTextUploadPath: 'rich-upload/',
    },
    local: {
      AK: 'ALTAK3fAE78I9f5oz2IHlhVU7q',
      SK: 'a726c9dc79834a8f8e0494bf26521119',
      Bucket: 'ayx-wly',
      Endpoint: 'https://ayx-wly.bj.bcebos.com',
      customHost: 'https://wly-oss.yunxiao.com',
      baseDir: 'test/mmd/',
      uploadPath: 'upload/',
      richTextUploadPath: 'rich-upload/',
    },
  }[env('SERVER', 'prod')]

  return {
    // ...
    upload: {
      objectStorage: {
        target: 'bos',
        baseDir: bosConfig.baseDir,
        uploadPath: bosConfig.uploadPath,
        richTextUploadPath: bosConfig.richTextUploadPath,
        customHost: bosConfig.customHost,

        config: {
          // Bos Config
          AK: bosConfig.AK,
          SK: bosConfig.SK,
          Bucket: bosConfig.Bucket,
          Endpoint: bosConfig.Endpoint
        }
      },
      verifyData: {
        appid: "wx7090745609fd7d58",
        secret: "6d6bf1347215d810c5d998f4355cd331"
      },
    }
  }
}
