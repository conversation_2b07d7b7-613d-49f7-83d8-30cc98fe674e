module.exports = {
  load: {
    before: [
      'responseWrapper',
      'koaBody',
      'responseTime',
      'logger',
      'cors',
      'responses',
      'gzip',
    ],
    order: [
      'originFilter',
    ],
    after: [
      'koaProxy',
      'parser',
      'router',
    ],
  },
  settings: {
    cors: {
      origin (ctx) {
        const baseOrigin = [
          'http://localhost:30001',
          'http://************:30001',
          'https://************:30001',
          'https://**********:8892',
          'http://**********:8892',
          'http://**********:8880',
          'https://**********:8880',
          'http://************:8880'
        ]
        const requestOrigin = ctx.accept.headers.origin
        const testOrigin = /(127\.0\.0\.1|172\.16\.16\.10|172\.18\.39\.143|10\.12\.40\.250|localhost)/.test(
          requestOrigin)
          ? [ctx.accept.headers.origin]
          : []
        const externalOrigin = /(yunxiao.com|meimeida.net|p0.cn)/.test(
          requestOrigin)
          ? [ctx.accept.headers.origin]
          : []
        return [
          ...baseOrigin,
          ...testOrigin,
          ...externalOrigin,
        ]
      },
      headers: [
        'Content-Type',
        'Authorization',
        'Origin',
        'Accept',
        'Cache-Control',
        'X-Space',
        'apikey',
        'X-From'],
    },
    responseWrapper: {
      enabled: true,
    },
    spaceFilter: {
      enabled: true,
    },
    originFilter: {
      enabled: true,
    },
    koaProxy: {
      enabled: true,
    },
    parser: {
      formLimit: '1024mb',
      formidable: {
        maxFileSize: 1024 * 1024 * 1024, // multipart data, modify here limit of uploaded file size
      },
    },
  },
}
