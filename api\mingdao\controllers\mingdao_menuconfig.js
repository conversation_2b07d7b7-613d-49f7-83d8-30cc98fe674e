'use strict'

const { BranchCurdRouter } = require('accel-utils')
const { size } = require('lodash')

/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#core-controllers)
 * to customize this controller
 */
const branchCurdRouter = new (class extends BranchCurdRouter {
  constructor(modelName) {
    super(modelName)
  }

})('mingdao_menuconfig')

module.exports = {
  ...branchCurdRouter.createHandlers(),

  async openGetAppConfig(ctx) {

    const params = ctx.query
    if (!params) {
      return ctx.throw(400, '参数错误')
    }

    const config = await strapi.query('mingdao_menuconfig').find(params)
    if (!size(config)) return ctx.send([{}])
    return ctx.send(config)

  }
}
