{"name": "strapi", "version": "3.6.8", "description": "An open source headless CMS solution to create and manage your own API. It provides a powerful dashboard and features to make your life easier. Databases supported: MongoDB, MySQL, MariaDB, PostgreSQL, SQLite", "homepage": "https://strapi.io", "directories": {"lib": "./lib", "bin": "./bin", "packages": "./packages"}, "main": "./lib", "bin": {"strapi": "./bin/strapi.js"}, "dependencies": {"@koa/cors": "^3.0.0", "async": "^2.1.2", "boom": "^7.3.0", "boxen": "4.2.0", "chalk": "^4.1.1", "chokidar": "3.5.1", "ci-info": "3.1.1", "cli-table3": "^0.6.0", "commander": "6.1.0", "configstore": "5.0.1", "cross-spawn": "^7.0.3", "debug": "^4.1.1", "delegates": "^1.0.0", "dotenv": "8.2.0", "execa": "^1.0.0", "fs-extra": "^9.1.0", "glob": "^7.1.2", "inquirer": "^6.2.1", "is-docker": "2.2.1", "koa": "^2.13.1", "koa-body": "^4.2.0", "koa-compose": "^4.1.0", "koa-compress": "^5.0.1", "koa-convert": "^2.0.0", "koa-favicon": "^2.0.0", "koa-i18n": "^2.1.0", "koa-ip": "^2.0.0", "koa-locale": "~1.3.0", "koa-lusca": "~2.2.0", "koa-router": "^7.4.0", "koa-session": "^6.2.0", "koa-static": "^5.0.0", "lodash": "4.17.21", "node-fetch": "2.6.1", "node-machine-id": "1.1.12", "node-schedule": "1.3.2", "opn": "^5.3.0", "ora": "^5.4.0", "package-json": "6.5.0", "qs": "^6.10.1", "resolve-cwd": "^3.0.0", "rimraf": "^3.0.2", "semver": "7.3.5", "verror": "^1.10.0", "mongoose": "5.10.8", "mongoose-float": "^1.0.4", "mongoose-long": "^0.3.2", "p-map": "4.0.0", "pluralize": "^8.0.0", "bookshelf": "^1.0.1", "date-fns": "^2.19.0"}, "scripts": {"postinstall": "node lib/utils/success.js"}, "author": {"email": "<EMAIL>", "name": "Strapi team", "url": "https://strapi.io"}, "maintainers": [{"name": "Strapi team", "email": "<EMAIL>", "url": "https://strapi.io"}], "repository": {"type": "git", "url": "git://github.com/strapi/strapi.git"}, "bugs": {"url": "https://github.com/strapi/strapi/issues"}, "engines": {"node": ">=16.20.0", "npm": ">=6.0.0"}, "license": "SEE LICENSE IN LICENSE", "keywords": ["strapi", "cms", "cmf", "content management system", "content management framework", "api", "auth", "framework", "http", "json", "koa", "koajs", "lusca", "mvc", "o<PERSON>h", "oauth2", "orm", "rest", "restful", "security", "jam", "jamstack", "javascript", "headless", "MongoDB", "MySQL", "MariaDB", "PostgreSQL", "SQLite", "graphqL", "infrastructure", "backend", "open source", "self hosted", "javascript", "lerna", "<PERSON><PERSON><PERSON><PERSON>", "react", "reactjs"], "gitHead": "231263a3535658bab1e9492c6aaaed8692d62a53"}