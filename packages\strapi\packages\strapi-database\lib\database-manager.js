'use strict'

const _ = require('lodash')

const { createQuery } = require('./queries')
const createConnectorRegistry = require('./connector-registry')
const constants = require('./constants')
const { validateModelSchemas } = require('./validation')
const createMigrationManager = require('./migration-manager')
const createLifecycleManager = require('./lifecycle-manager')
const { toArray } = require('lodash')

class DatabaseManager {
  constructor (strapi) {
    this.strapi = strapi

    this.initialized = false

    this.connectors = createConnectorRegistry({
      connections: strapi.config.get('database.connections'),
      defaultConnection: strapi.config.get('database.defaultConnection'),
    })

    this.queries = new Map()
    this.models = new Map()

    this.migrations = createMigrationManager(this)
    this.lifecycles = createLifecycleManager()
  }

  async initialize () {
    if (this.initialized === true) {
      throw new Error('Database manager already initialized')
    }

    this.initialized = true

    this.connectors.load()

    validateModelSchemas({ strapi: this.strapi, manager: this })

    this.initializeModelsMap()

    await this.connectors.initialize()

    return this
  }

  async destroy () {
    await Promise.all(this.connectors.getAll().map(connector => connector.destroy()))
  }

  initializeModelsMap () {
    Object.keys(this.strapi.models).forEach(modelKey => {
      const model = this.strapi.models[modelKey]
      this.models.set(model.uid, model)
    })

    Object.keys(this.strapi.plugins).forEach(pluginKey => {
      Object.keys(this.strapi.plugins[pluginKey].models).forEach(modelKey => {
        const model = this.strapi.plugins[pluginKey].models[modelKey]
        this.models.set(model.uid, model)
      })
    })
  }

  query (entity, plugin) {
    const model = this.getModel(entity, plugin)

    if (!model) {
      throw new Error(`The model ${entity} can't be found.`)
    }

    if (this.queries.has(model.uid)) {
      return this.queries.get(model.uid)
    }

    const connectorQuery = this.connectors
      .get(model.orm)
      .queries({ model, modelKey: model.modelName, strapi })

    const query = createQuery({
      connectorQuery,
      model,
    })

    this.queries.set(model.uid, query)
    return query
  }

  getModel (name, plugin) {
    if (!name || name === '*') return

    /* Static Model */
    let modelKey = _.toLower(name)
    let modelName = modelKey, pluginName = plugin
    // Pattern uid match eg. strapi::core-store plugin::upload.file
    if (this.models.has(modelKey)) {
      const model = this.models.get(modelKey)
      modelName = model.modelName
      pluginName = model.plugin
    }
    // Name Resolve
    else {
      if (name.includes('::') && !plugin) {
        const [, nameStr] = name.split('::')
        if (nameStr.includes('.')) {
          [pluginName, modelName] = nameStr.split('.')
        } else {
          modelName = nameStr
        }
      }
    }
    let model
    if (pluginName) {
      model = _.get(strapi.plugins, [pluginName, 'models', modelName])
    } else {
      model = _.get(strapi, ['models', modelName]) || _.get(strapi, ['components', modelName])
    }

    // Simple Name Match
    if (!model) {
      const allModels = [
        ...toArray(strapi.models),
        ...toArray(strapi.components),
        ...Object.keys(strapi.plugins).map(key => {
          return toArray(strapi.plugins[key].models)
        }).flat()
      ]
      for (let matchModel of allModels) {
        if (matchModel.modelName === modelName || matchModel.globalId === modelName) {
          model = matchModel
          break
        }
      }
    }

    /* Dynamic Model */
    if (!model) {
      for (let key of Object.keys(strapi.models)) {
        const matchModel = strapi.models[key]
        // Mingdao ID & Alias
        if (matchModel.orm === 'mingdao') {
          if (matchModel.modelName === modelName || matchModel.globalId === modelName) {
            model = matchModel
            break
          }
        }
      }
    }
    return model
  }

  getModelByAssoc (assoc) {
    return this.getModel(assoc.collection || assoc.model || assoc.targetModel, assoc.plugin)
  }

  getModelByCollectionName (collectionName) {
    return Array.from(this.models.values()).find(model => {
      return model.collectionName === collectionName
    })
  }

  getModelByGlobalId (globalId) {
    return Array.from(this.models.values()).find(model => {
      return model.globalId === globalId
    })
  }

  getModelsByAttribute (attr) {
    if (attr.type === 'component') {
      return [this.getModel(attr.component)]
    }
    if (attr.type === 'dynamiczone') {
      return attr.components.map(compoName => this.getModel(compoName))
    }
    if (attr.model || attr.collection) {
      return [this.getModelByAssoc(attr)]
    }

    return []
  }

  getModelsByPluginName (pluginName) {
    if (!pluginName) {
      return strapi.models
    }

    return strapi.plugins[pluginName].models
  }

  getReservedNames () {
    return {
      models: constants.RESERVED_MODEL_NAMES,
      attributes: [
        ...constants.RESERVED_ATTRIBUTE_NAMES,
        ...(strapi.db.connectors.default.defaultTimestamps || []),
      ],
    }
  }
}

function createDatabaseManager (strapi) {
  return new DatabaseManager(strapi)
}

module.exports = {
  createDatabaseManager,
}
