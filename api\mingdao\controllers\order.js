const mingdaoApi = require('../utils/mingdaoApi')
const { genMiniprogramACode } = require('strapi-plugin-users-permissions/utils/wechat')
const fs = require('fs')
const { objectStorageConfig } = require('plugin-upload/config/object-storage')
const crypto = require('crypto')
// const mingdaoConfig = strapi.config.mingdao
const worksheet = strapi.config.mingdao.worksheet
const worksheetId = worksheet.order
async function getDetail(ctx) {
  let { configId, orderNo, isRelation } = ctx.request.query
  if (!orderNo) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误，orderNo 不能为空')
  }

  // 多租户支付为不影响线上美美哒使用 兼容处理
  // TODO 后续删除
  if (!configId || configId === 'undefined') {
    configId = '663f0d852a31b6deeacb2c6e'
  }

  const mingdaoConfig = await mingdaoApi.getMingdaoConfig(configId);
  const pBranchId = mingdaoConfig.pBranch.id;

  const row = await getDetailByOrderNo(orderNo, pBranchId, isRelation === '1' || isRelation === 'true')

  if (!row) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '订单不存在')
  }

  return ctx.wrapper.succ(row)
}

async function getDetailById(ctx) {
  let { configId, id, isRelation } = ctx.request.query
  if (!id) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误，orderNo 不能为空')
  }

  // 多租户支付为不影响线上美美哒使用 兼容处理
  // TODO 后续删除
  if (!configId || configId === 'undefined') {
    configId = '663f0d852a31b6deeacb2c6e'
  }

  const mingdaoConfig = await mingdaoApi.getMingdaoConfig(configId);
  const pBranchId = mingdaoConfig.pBranch.id;

  const row = await getDetailByOrderId(id, pBranchId, isRelation === '1' || isRelation === 'true')

  if (!row) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '订单不存在')
  }

  return ctx.wrapper.succ(row)
}

async function getDetailByOrderId(id, pBranchId, isRelation = false) {
  let rowData = await mingdaoApi.getRowById(worksheetId, pBranchId, id)
  if (rowData && isRelation) {
    const relation = {
      worksheetId: worksheetId,
      fields: [
        {
          controlId: 'receiveAddress',
        },
        {
          controlId: 'customer',
        },
        {
          controlId: 'goodsDetails',
        }
      ]
    }
    await mingdaoApi.getRelations(rowData, relation, pBranchId)
  }
  return rowData
}

async function getDetailByOrderNo(orderNo, pBranchId, isRelation = false) {
  const filters = [{
    'controlId': 'orderNo',
    'dataType': 2,
    'spliceType': 1,
    'filterType': 2,
    'value': orderNo
  }]
  let result = await mingdaoApi.getList(worksheetId, pBranchId, filters)
  let list = result && result.rows
  if (list && list.length) {
    const rowData = list[0]
    if (isRelation) {
      const relation = {
        worksheetId: worksheetId,
        fields: [
          {
            controlId: 'receiveAddress',
          },
          {
            controlId: 'customer',
          },
          {
            controlId: 'goodsDetails',
          }
        ]
      }
      await mingdaoApi.getRelations(rowData, relation, pBranchId)
    }
    return rowData;
  }
  return null
}

async function getOrderList(ctx) {
  const { type, pageIndex = 1, pageSize = 20 } = ctx.request.query

  const userId = ctx.state.user.id
  const userService = strapi.plugins['users-permissions'].services.user
  const user = await userService.findOne({ id: userId })
  const pBranchId = user.pBranch.id;

  if (!user.customId) {
    return ctx.wrapper.succ([])
  }

  const filters = [{
    'controlId': 'customer',
    'dataType': 29, // 关联表格
    'spliceType': 1,
    'filterType': 24, //关联过滤
    'value': user.customId
  }]

  if (type && type !== '全部') {
    filters.push({
      'controlId': 'orderStatus',
      'dataType': 2,
      'spliceType': 1,
      'filterType': 2,
      'value': type
    })
  }
  let list = await mingdaoApi.getList(worksheetId, pBranchId, filters, pageIndex, pageSize)
  return ctx.wrapper.succ(list)
}

async function notifyMingdaoPaySuccess(orderNo, pBranchId, payment_time, transaction_id, pay_type, pay_amount) {
  // https://api.mingdao.com/workflow/hooks/NjU4MjllN2Y0ZjJhZDUxNTk5ZGM5ZjZl
  let data = {
    orderNo: orderNo,
    payment_time: payment_time,
    transaction_id: transaction_id,
    pay_type: pay_type,
    pay_amount: pay_amount * 0.01
  }
  const mingdaoConfig = await mingdaoApi.getMingdaoConfig(null, pBranchId);
  if (!mingdaoConfig) throw new Error('pBranchId err');

  const result = await mingdaoApi.notify(mingdaoConfig.pay_success_notice_url, data)
  if (result.status === 1) {
    console.log('----- notify success -----', result)
  } else {
    console.log('----- notify fail -----', result)
  }
}

async function notifyMingdaoRefund(afterSaleNo, pBranchId, refund_time, refund_trx_id, refund_amount, refund_status, refund_fail_reason) {
  //https://api.mingdao.com/workflow/hooks/NjU4MjllNjhjZTI1NGUwZTM0ZjVhYzJm
  // 退款成功
  if (refund_status === 'SUCCESS') {
    const data = {
      afterSaleNo: afterSaleNo,
      refund_time: refund_time,
      refund_trx_id: refund_trx_id,
      refund_amount: refund_amount * 0.01,
      refund_fail_reason: refund_fail_reason
    }

    const mingdaoConfig = await mingdaoApi.getMingdaoConfig(null, pBranchId);
    if (!mingdaoConfig) throw new Error('pBranchId err');

    const result = await mingdaoApi.notify(mingdaoConfig.refund_success_notice_url, data)
    if (result.status === 1) {
      console.log('----- notify success -----', result)
    } else {
      console.log('----- notify fail -----', result)
    }
  } else if (refund_status === 'ABNORMAL') {
    const data = {
      afterSaleNo: afterSaleNo,
      refund_trx_id: refund_trx_id,
      refund_fail_reason: refund_fail_reason
    }
    const mingdaoConfig = await mingdaoApi.getMingdaoConfig(null, pBranchId);

    const result = await mingdaoApi.notify(mingdaoConfig.refund_fail_notice_url, data)
    if (result.status === 1) {
      console.log('----- notify success -----', result)
    } else {
      console.log('----- notify fail -----', result)
    }
  }
}

async function getACodeUrl(ctx) {
  let { appId, orderNo } = ctx.request.query
  if (!orderNo) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误，orderNo 不能为空')
  }

  const app = await _getAppById(appId)
  if (!app) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'APP ID 错误')
  }
  const verifyData = app.verifyData
  const encoding = 'binary'

  const data = await genMiniprogramACode({
    appid: verifyData.appid,
    secret: verifyData.secret,
    path: `pages/order/detail?orderNo=${orderNo}&from=mingdao`,
    encoding: encoding
  })

  const filename = `${orderNo}.jpeg`
  // 先将data 写入文件，然后上传。 再删除文件
  // 获取项目所在目录
  const projectPath = process.cwd()

  const filePath = projectPath + '/' + filename
  fs.writeFileSync(filePath, Buffer.from(data, encoding))

  const iUploadPlugin = strapi.plugins['upload']
  const service = iUploadPlugin.services['object-storage']
  const objectStorageConfig = iUploadPlugin.config.objectStorageConfig
  const dir = [objectStorageConfig.baseDir, objectStorageConfig.uploadPath].filter(e => e).join('')
  let uploadInfo = await service.uploadFileToBucket(filename, filePath, dir + 'mmd-order/')

  // 删除文件
  fs.unlinkSync(filePath)

  console.log('----- uploadInfo -----', uploadInfo.url)
  return ctx.wrapper.succ({
    key: uploadInfo.key,
    url: uploadInfo.url
  })
}

async function _getAppById(id) {
  const iPlugin = strapi.plugins['users-permissions']
  if (id.toString().length === 24) {
    return await iPlugin.services['app'].findOne({ id })
  }
  return await iPlugin.services['app'].findOne({ id })
}


module.exports = {
  getDetail,
  getDetailById,
  getOrderList,
  getDetailByOrderNo,
  notifyMingdaoPaySuccess,
  notifyMingdaoRefund,
  getACodeUrl
}
