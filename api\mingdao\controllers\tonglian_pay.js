const uuid = require('uuid')
const tonglianApi = require('../utils/tonglianApi')
const { notifyMingdaoRefund, notifyMingdaoPaySuccess } = require('./order')
const moment = require('moment')
// const cusId = "5634910503950CR"
const crypto = require('crypto');
const axios = require('axios')
const mingdaoApi = require('../utils/mingdaoApi')

// 生成商户订单号（UUID）
function _genOrderUUID() {
  return uuid.v4().split('-').join('');               // 商户系统内部的订单号,32个字符内、可包含字母
}

// 1. 小程序创建支付单
async function createPayOrder(ctx, mingdaoConfig, orderNo, amountPrice, openid, wxAppId, payType) {
  // https://aipboss.allinpay.com/know/devhelp/main.php?pid=15#mid=88
  // Request
  // - cusid：实际交易的商户号
  // - appid：平台分配的APPID
  // - version: 默认填11
  // - trxamt：单位为分
  // - reqsn：商户的交易订单号。 保证商户平台唯一
  // - paytype：交易方式
  // - randomstr：商户自行生成的随机字符串
  // - notify_url：支付回调通知URL，该地址必须为直接可访问的URL，不允许携带查询串
  // - body：订单商品名称，为空则以商户名作为商品名称
  // - acct：微信小程序-用户小程序的openid， 微信支付-用户的微信openid，支付宝支付-用户user_id
  // - sub_appid：微信小程序/微信公众号/APP的appid
  // - signtype：签名类型:
  // - sign：签名

  const tlConfig = mingdaoConfig.tong_lian;
  const pBranchId = mingdaoConfig.pBranch.id;
  if (!tlConfig) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '通联支付配置异常')
  }

  // const orderRecord = await strapi.services['mingdao_order'].findOne({
  //   mingdao_order_no: orderNo,
  //   payment_type: 2,
  //   payment_channel: payType === 'W06' ? 0 : 1, // W06 小程序
  //   _sort: 'id:DESC'
  // })

  let outTradeNo = _genOrderUUID();
  const params = {
    cusid: tlConfig.cus_id,
    appid: tlConfig.app_id,
    version: 11,
    trxamt: amountPrice,
    unireqsn: outTradeNo,
    paytype: payType,// W01	微信扫码支付 W02	微信JS支付 W06	微信小程序支付
    randomstr: _genOrderUUID(),
    notify_url: `${strapi.config.server.serverWanUrl}/tonglian/actions/callbackPayOrder/${mingdaoConfig.id}`,
    body: `${mingdaoConfig.name}-${orderNo}`,
    acct: openid,
    sub_appid: wxAppId,
    signtype: 'RSA'
  }
  params.sign = _sign(params, tlConfig.private_key)
  const result = await tonglianApi.pay(params)
  if (result.retcode !== 'SUCCESS' || result.errmsg) {
    return ctx.wrapper.error('HANDLE_ERROR', result.retmsg || result.errmsg || '支付异常')
  }
  await strapi.services['mingdao_order'].create({
    order_no: outTradeNo,
    mingdao_order_no: orderNo,
    transaction_id: result.trxid,
    pay_status: 0,
    order_price: amountPrice,
    payment_type: 2,
    payment_channel: payType === 'W06' ? 0 : 1, // W06 小程序
    payment_merchant: tlConfig.cus_id,
    pBranch: pBranchId,
  })

  // // 小程序调起支付参数
  // const signType = 'RSA'
  // const appId = APP_ID
  // const timeStamp = parseInt(+new Date() / 1000 + '').toString()
  // const nonceStr = randomString(32)
  // const orderPackage = `prepay_id=${prepayId}`
  // const paySign = wxPay.sha256WithRsa(`${appId}\n${timeStamp}\n${nonceStr}\n${orderPackage}`)
  return ctx.wrapper.succ({
    orderId: outTradeNo,
    payType: 2,
    payInfo: result
  })
}

// 2.【客户端】小程序调起支付API
// https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_5_4.shtml

// 4.1. H5收银台订单提交接口
async function getH5PayParams(ctx, mingdaoConfig, orderNo, amountPrice, returl) {
  // https://aipboss.allinpay.com/know/devhelp/main.php?pid=38#mid=313
  // Request
  // - cusid：实际交易的商户号
  // - appid：平台分配的APPID
  // - version: 默认填11
  // - trxamt：单位为分
  // - reqsn：商户的交易订单号。 保证商户平台唯一
  // - charset：参数字符编码集。商户网站使用的编码格式，支持 UTF-8、GBK。跟商户网站的编码一致
  // - returl： 页面跳转同步通知页面路径。交易完成后，平台会按照此地址将用户的交易结果页面重定向到商户网站。同时该参数为返回商户按钮的指向链接。 必须为https协议地址，且不允许带参数
  // - notify_url：支付回调通知URL，该地址必须为直接可访问的URL，不允许携带查询串
  // - body：订单商品名称，为空则以商户名作为商品名称
  // - randomstr：商户自行生成的随机字符串
  // - signtype：签名类型:
  // - sign：签名

  const tlConfig = mingdaoConfig.tong_lian;
  const pBranchId = mingdaoConfig.pBranch.id;
  if (!tlConfig) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '通联支付配置异常')
  }

  // const payOrder = await strapi.services['mingdao_order'].findOne({ mingdao_order_no: orderNo, pay_status: 1 })
  // if (payOrder) {
  //   return ctx.wrapper.error('PARAMETERS_ERROR', '请勿重复支付')
  // }

  // const orderRecord = await strapi.services['mingdao_order'].findOne({
  //   mingdao_order_no: orderNo,
  //   payment_type: 2,
  //   payment_channel: 2, // H5
  //   _sort: 'id:DESC'
  // })

  // if (orderRecord) {
  //   let orderInfo = await _checkPayOrder(orderRecord.order_no, mingdaoConfig);
  //   if (orderInfo.trxstatus === '0000') {
  //     return ctx.wrapper.error('PARAMETERS_ERROR', '请勿重复支付')
  //   }
  // }

  let outTradeNo = _genOrderUUID();
  // let outTradeNo = orderRecord?.order_no || _genOrderUUID();
  const params = {
    cusid: tlConfig.cus_id,
    appid: tlConfig.app_id,
    version: 11,
    trxamt: amountPrice,
    unireqsn: outTradeNo,
    charset: 'UTF-8',
    returl: returl,
    // notify_url: `${strapi.config.server.serverWanUrl}/tonglian/actions/callbackPayOrder`,
    notify_url: `${strapi.config.server.serverWanUrl}/tonglian/actions/callbackPayOrder/${mingdaoConfig.id}`,
    body: `${mingdaoConfig.name}-${orderNo}`,
    randomstr: _genOrderUUID(),
    signtype: 'RSA'
  }
  params.sign = _sign(params, tlConfig.private_key)

  await strapi.services['mingdao_order'].create({
    order_no: outTradeNo,
    mingdao_order_no: orderNo,
    pay_status: 0,
    order_price: amountPrice,
    payment_type: 2,
    payment_channel: 2, // H5
    payment_merchant: tlConfig.cus_id,
    pBranch: pBranchId,
  })
  return ctx.wrapper.succ(params)
}

// 3. 接收支付结果通知
// https://aipboss.allinpay.com/know/devhelp/main.php?pid=15#mid=94
/**
 *
 * @param ctx
 * @param ctx.request
 * @param ctx.request.body
 * @param ctx.request.body.resource
 * @param {string} ctx.request.body.resource.ciphertext
 * @param {string} ctx.request.body.resource.associated_data
 * @param {string} ctx.request.body.resource.nonce
 */
async function callbackPayOrder(ctx) {
  console.log('----notify---', ctx.request.origin)

  const data = ctx.request.body
  console.log('----notify2----', data)
  try {
    // 通知参数

    // appid - 收银宝APPID
    // outtrxid - 第三方交易号  暂未启用
    // trxcode - 交易类型
    // trxid - 收银宝交易单号
    // initamt - 原始下单金额 与请求trxamt值一致
    // trxamt - 交易金额
    // trxdate - 交易请求日期
    // paytime - 交易完成时间
    // trxstatus - 交易结果码  交易状态详见交易返回码说明
    // cusid - 商户号
    // cusorderid - 业务流水 统一下单对应的reqsn订单号
    // sign - 签名

    const { id } = ctx.params
    const mingdaoConfig = await mingdaoApi.getMingdaoConfig(id);
    const pBranchId = mingdaoConfig.pBranch.id;

    const { trxstatus, cusorderid, trxid, paytime, trxamt } = data
    const paymentTime = formatTime(paytime)
    if (trxstatus === '0000') {
      await strapi.services['mingdao_order'].update({
        order_no: cusorderid
      }, {
        pay_status: 1,
        transaction_id: trxid,
        payment_time: paymentTime,
        payInfo: data,
      })

      const orderRecord = await strapi.services['mingdao_order'].findOne({ order_no: cusorderid, pBranch: pBranchId })

      // 支付成功后更新 明道云订单状态
      console.log('----notify3----', data)
      notifyMingdaoPaySuccess(orderRecord.mingdao_order_no, pBranchId, paymentTime, trxid, '通联支付', trxamt)
    }
  } catch (e) {
    console.log('Error:', e)
    return { 'code': 'FAIL', 'message': e.toString() }
  }
  return { 'code': 'SUCCESS', 'message': '' }
}

// https://aipboss.allinpay.com/know/devhelp/main.php?pid=15#mid=836
async function checkPayOrder(ctx) {
  const { id } = ctx.query
  if (!id) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误，id 不能为空')
  }
  const result = await _checkPayOrder(id);
  return ctx.wrapper.succ(result)
}

async function _checkPayOrder(id, mingdaoConfig) {
  // - cusid：实际交易的商户号
  // - appid：平台分配的APPID
  // - reqsn：商户的交易订单号。 保证商户平台唯一
  // - randomstr：商户自行生成的随机字符串
  // - signtype：签名类型:
  // - sign：签名
  const tlConfig = mingdaoConfig.tong_lian;

  const params = {
    cusid: tlConfig.cus_id,
    appid: tlConfig.app_id,
    version: 11,
    reqsn: id,
    randomstr: _genOrderUUID(),
    signtype: 'RSA'
  }
  params.sign = _sign(params, tlConfig.private_key)

  const result = await tonglianApi.query(params)
  console.log('params', result)
  return result;
}

// https://aipboss.allinpay.com/know/devhelp/main.php?pid=15#mid=838
async function refund(ctx, mingdaoConfig, order, refund_id, amount, reason) {
  console.log('---- tonglian refund ----')
  // - cusid：实际交易的商户号
  // - appid：平台分配的APPID
  // - trxamt：退款金额，单位为分
  // - reqsn：商户的退款交易订单号。 保证商户平台唯一
  // - oldreqsn：原交易订单号
  // - oldtrxid：原交易流水单号
  // - remark：退款备注
  // - randomstr：商户自行生成的随机字符串
  // - signtype：签名类型:
  // - sign：签名

  const tlConfig = mingdaoConfig.tong_lian;
  const pBranchId = mingdaoConfig.pBranch.id;
  if (!tlConfig) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '通联支付配置异常')
  }
  let out_refund_no = _genOrderUUID() //自己生成退款单号

  const params = {
    cusid: tlConfig.cus_id,
    appid: tlConfig.app_id,
    version: 11,
    trxamt: amount,
    reqsn: out_refund_no,
    oldreqsn: order.order_no,
    oldtrxid: order.transaction_id,
    remark: reason,
    randomstr: _genOrderUUID(),
    signtype: 'RSA'
  }
  params.sign = _sign(params, tlConfig.private_key)

  const result = await tonglianApi.refund(params)
  console.log('---- tonglian refund ----', result)

  // - cusid: 实际交易的商户号
  // - appid: 平台分配的APPID
  // - trxid: 收银宝退款交易单号
  // - reqsn: 商户的退款交易订单号。
  // - trxstatus: 交易状态
  // - fintime: 退款完成时间
  const isSuccess = result.retcode === 'SUCCESS' && result.trxstatus === '0000'
  const errorMsg = isSuccess ? null : result.retmsg || result.errmsg || '退款失败'
  const refundTime = formatTime(result.fintime)
  await strapi.services['mingdao_refund'].create({
    refund_no: out_refund_no,
    third_refund_no: refund_id,
    transaction_id: result.trxid,
    order: order.id,
    refund_reason: reason,
    refund_status: isSuccess ? 1 : 2,
    refund_time: refundTime,
    refund: amount,
    fail_reason: errorMsg,
    refundParams: params,
    refundInfo: result,
    pBranch: pBranchId,
  })

  // 退款后更新 明道云售后状态
  // afterSaleNo
  // refund_time
  // refund_trx_id
  // refund_amount
  // refund_status
  // refund_fail_reason
  notifyMingdaoRefund(
    refund_id,
    pBranchId,
    refundTime,
    result.trxid,
    amount,
    isSuccess ? 'SUCCESS' : 'ABNORMAL',
    errorMsg)
  if (isSuccess) {
    return ctx.wrapper.succ(result)
  } else {
    // 退款失败
    return ctx.wrapper.error('HANDLE_ERROR', errorMsg)
  }
}

function formatTime(time) {
  if (!time) {
    return time
  }
  // 在字符串第6位增加一个空格
  if (time.length === 14) {
    time = time.slice(0, 8) + ' ' + time.slice(8)
  }
  return moment(time).format('YYYY-MM-DD HH:mm:ss')
}

function _sign(params, privateKey) {
  const keys = Object.keys(params).sort()
  console.log('-', keys)
  const str = keys.map(key => `${key}=${params[key]}`).join('&')
  const sign = crypto.createSign('SHA1');
  console.log('-', str)
  sign.update(str);
  sign.end();
  let finalPrivateKey = `-----BEGIN RSA PRIVATE KEY-----\n${privateKey}\n-----END RSA PRIVATE KEY-----`
  return sign.sign(finalPrivateKey, 'base64');
}

module.exports = {
  createPayOrder,
  getH5PayParams,
  callbackPayOrder,
  checkPayOrder,
  refund
}
