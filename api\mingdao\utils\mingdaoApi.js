const axios = require('axios')

// 缓存有效时长
const cacheExpirationTime = 5 * 60 * 1000
// 缓存数据
let configCachedData = null
// 缓存失效时间
let cacheExpiration = null

async function _getAllConfig() {
  const list = await strapi.query('mingdao_config').find()
  return list;
}

async function getMingdaoConfig(id, pBranchId) {
  let configList;
  if (configCachedData && cacheExpiration && cacheExpiration > Date.now()) {
    configList = configCachedData
  } else {
    try {
      configList = await _getAllConfig()
      configCachedData = configList
      cacheExpiration = Date.now() + cacheExpirationTime
    } catch (e) {
      if (!configCachedData) throw e
      // 获取出错暂时使用缓存数据
      cacheExpiration = Date.now() + cacheExpirationTime
      configList = configCachedData
      strapi.log.warn(`获取配置失败，使用缓存数据`)
    }
  }
  if (id) {
    const mingdaoConfig = configList.find(e => e.id === id)
    if (!mingdaoConfig) throw new Error('pBranchId err');
    return mingdaoConfig;
  }
  if (pBranchId) {
    const mingdaoConfig = configList.find(e => e.pBranch?.id === pBranchId)
    if (!mingdaoConfig) throw new Error('pBranchId err');
    return mingdaoConfig;
  }
  throw new Error('getMingdaoConfig err');
}

async function getAllList(worksheetId, configId, filter = [], pageIndex = 1, pageSize = 50, list = []) {
  const mingdaoConfig = await getMingdaoConfig(configId);
  const res = await getList(worksheetId, mingdaoConfig.pBranch.id, filter, pageIndex, pageSize)

  list = list.concat(...res.rows)
  if (list.length < res.total) {
    pageIndex++;
    return getAllList(worksheetId, configId, filter, pageIndex, pageSize, list)
  }
  return list
}

async function getList(worksheetId, pBranchId, filter = [], pageIndex = 1, pageSize = 50) {
  const mingdaoConfig = await getMingdaoConfig(null, pBranchId);

  let params = {
    'appKey': mingdaoConfig.app_key,
    'sign': mingdaoConfig.sign,
    'worksheetId': worksheetId,
    'pageIndex': pageIndex,
    'pageSize': pageSize,
    'filters': filter
  }
  const res = await axios.post(`${mingdaoConfig.base_url}/v2/open/worksheet/getFilterRows`, params)
  let result = res.data
  if (result && result.error_code && result.error_code === 1) {
    return result.data || { rows: [], total: 0 }
  }

  return { rows: [], total: 0 }
}

async function getRowById(worksheetId, pBranchId, rowId) {
  const mingdaoConfig = await getMingdaoConfig(null, pBranchId);

  let params = {
    'appKey': mingdaoConfig.app_key,
    'sign': mingdaoConfig.sign,
    'worksheetId': worksheetId,
    'rowId': rowId
  }
  const res = await axios.get(`${mingdaoConfig.base_url}/v2/open/worksheet/getRowById`, { params })
  let result = res.data
  if (result && result.error_code && result.error_code === 1) {
    return result.data
  }
  return null
}

async function getRowRelations(worksheetId, pBranchId, rowId, controlId, list = []) {
  const mingdaoConfig = await getMingdaoConfig(null, pBranchId);

  let params = {
    'appKey': mingdaoConfig.app_key,
    'sign': mingdaoConfig.sign,
    'worksheetId': worksheetId,
    'rowId': rowId,
    'controlId': controlId
  }
  const res = await axios.post(`${mingdaoConfig.base_url}/v2/open/worksheet/getRowRelations`, params)
  let result = res.data
  if (result && result.error_code && result.error_code === 1) {
    list = list.concat(...result.data.rows)
    let total = result.data.total
    if (list.length < total) {
      return getRowRelations(worksheetId,pBranchId, rowId, controlId, list)
    }
  }
  return list
}

async function getRelations(item, relation, pBranchId) {
  let func = async function (field, item) {
    let relationItems = await getRowRelations(relation.worksheetId, pBranchId, item.rowid, field.controlId)
    if (relationItems && relationItems.length && field.fields && field.fields.length) {
      for (const relationItem of relationItems) {
        await getRelations(relationItem, field, pBranchId)
      }
    }
    item[field.controlId] = relationItems
  }

  await Promise.all(relation.fields.map((field) => {
    if (!item[field.controlId] || item[field.controlId] === '[]') {
      return
    }
    return func(field, item)
  }))
}

async function notify(url, data) {
  const res = await axios.post(url, data)
  return res.data
}


module.exports = {
  getMingdaoConfig,
  getAllList,
  getList,
  getRowById,
  getRowRelations,
  getRelations,
  notify
}
