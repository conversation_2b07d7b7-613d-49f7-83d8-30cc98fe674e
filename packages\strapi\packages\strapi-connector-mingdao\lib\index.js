const { omit } = require('lodash')
const { createOpenApiConnection } = require('../../../utils/mingdao-open-api')
const _ = require('lodash')
const {
  getAppSectionWorksheets,
  toMingdaoModel,
  mingdaoModelInstance,
} = require('../../../utils/mingdao-utils')
const queries = require('./queries')

const isMingdaoConnection = ({ connector }) => connector === 'mingdao'

module.exports = function (strapi) {
  const { connections } = strapi.config
  const connectionNames = Object.keys(connections).filter(key =>
    isMingdaoConnection(connections[key])
  )

  function createMingdaoInstance (connectionName) {
    const connection = connections[connectionName]
    const appConnection = createOpenApiConnection(connection.settings)

    async function initMingdao () {
      const app = await appConnection.getApp({ url: '/api/v1/open/app/get' })
      const worksheetItems = getAppSectionWorksheets(app.sections)
      return await Promise.all(worksheetItems.map(async (worksheetItem) => {
        const worksheetInfo = await appConnection.getWorksheetInfo(worksheetItem.id)
        const worksheet = {
          ...worksheetItem,
          ...worksheetInfo,
        }
        const model = toMingdaoModel(worksheet, { connection: appConnection })
        return mingdaoModelInstance(model)
      }))
    }

    return {
      initMingdao,
      ...appConnection
    }
  }

  return {
    async initialize () {
      console.info('MingDao initialize...')
      // 初始化 database 配置中的明道云应用连接
      await Promise.all(connectionNames.map(connectionName => {
        return (async () => {
          const mingdaoInstance = createMingdaoInstance(connectionName)
          const models = await mingdaoInstance.initMingdao()
          for (let model of models) {
            strapi.models[model.mingdao.id] = model
            strapi.contentTypes[model.uid] = omit(model, ['mingdao', 'connection'])
          }
          _.set(strapi, `connections.${connectionName}`, mingdaoInstance)
        })()
      }))
    },
    destroy () {
      console.info('MingDao destroy...')
      for (let connectionName of connectionNames) {
        const connection = strapi[`connections.${connectionName}`]
        for (let model of connection.models) {
          delete strapi.models[model.mingdao.id]
          delete strapi.contentTypes[model.uid]
        }
        _.unset(strapi, `connections.${connectionName}`)
      }
    },
    buildQuery () {
      console.info('MingDao buildQuery...')
    },
    queries,
    get defaultTimestamps () {
      return ['ctime', 'utime']
    },
  }
}
