'use strict';

module.exports = {
  "collectionName": "mingdao_user",
  "info": {
    "name": "mingdao_user",
    "label": "明道云用户配置",
    "description": "明道云用户配置"
  },
  "options": {
    "draftAndPublish": false,
    "timestamps": true,
    "allOf": [
      {
        "if": {
          "properties": {
            "type": {
              "const": "store"
            }
          }
        },
        "then": {
          "properties": {
            "associateClerk": { "visible": true },
            "clerkNumber": { "visible": true }
          }
        }
      }
    ]
  },
  "pluginOptions": {

  },
  "attributes": {
    "name": {
      "label": "登录名",
      "type": "string",
      required: true,
      "unique": true,
    },
    "password": {
      "label": "密码",
      "type": "string",
      required: true,
    },
    userName: {
      label: '用户名',
      type: 'string',
      size: 6,
    },
    sex: {
      label: '性别',
      type: 'enumeration',
      enum: ['男', '女'],
      size: 6,
    },
    mingdao_usertype: {
      label: '用户类型',
      model: 'mingdao_usertype',
      visible: true,
      required: true,
    },
    mingdao_configs: {
      label: '应用配置',
      model: 'mingdao_config',
      visible: true,
      required: true,
    },

    invitationCode: {
      label: '邀请码',
      type: 'number',
      visible: true,
    },
    vip: {
      label: '会员到期时间',
      type: 'datetime',
      visible: true,
    },
    type: {
      label: '类型',
      type: 'string',
      options: [
        {
          value: 'store',
          label: '店长'
        },
        {
          value: 'clerk',
          label: '店员'
        },
        {
          value: 'other',
          label: '其他'
        }
      ],
      visible: true,
      required: true
    },
    associateClerk: {
      label: '关联店员',
      model: 'mingdao_usertype',
      visible: false,
    },
    clerkNumber: {
      label: '店员数量',
      type: 'number',
      visible: true,
    },
    accountType: {
      label: '账户类型',
      type: 'string',
      visible: false,
      options: [
        {
          value: 'email',
          label: '邮箱'
        },
        {
          value: 'phone',
          label: '电话号码'
        }
      ],
    },
    "pBranch": {
      "label": "租户",
      "plugin": "users-permissions",
      "model": "branch",
      "configurable": false
    },
  }
}
