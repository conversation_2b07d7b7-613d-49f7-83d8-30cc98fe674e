const { convertRestQueryParams } = require('accel-utils')
const { isUndefined, isString } = require('lodash')

function getAppSectionWorksheets (sections, rootSectionId) {
  let worksheets = []
  for (const section of sections) {
    let sectionId = rootSectionId || section.sectionId
    for (let item of section.items) {
      if (item.type === 0) {
        worksheets.push({
          ...item,
          sectionId: sectionId
        })
      }
    }
    if (section.childSections) {
      worksheets = worksheets.concat(getAppSectionWorksheets(
        section.childSections, sectionId
      ))
    }
  }
  return worksheets
}

const mingdaoModelPrefix = 'mingdao::'

function toMingdaoModel (worksheet, { connection }) {
  const associations = []
  const controls = worksheet.controls
  const customControls = controls.filter(e => e.row !== 9999)
  const systemControls = controls.filter(e => {
    return e.row === 9999 && !/^wf/.test(e.controlId)
  })
  const wfControls = controls.filter(e => {
    return e.row === 9999 && /^wf/.test(e.controlId)
  })
  const baseFields = {
    orm: 'mingdao',
    modelType: 'contentType',
    modelName: `${worksheet.alias || worksheet.worksheetId}`,
    kind: 'collectionType',
    uid: `${mingdaoModelPrefix}${worksheet.worksheetId}`,
    globalId: worksheet.worksheetId,
    primaryKey: 'id',
    info: {
      name: worksheet.name,
      description: worksheet.notes,
    },
    options: {
      timestamps: true
    },
    attributes: customControls.reduce((acc, control) => {
      let attribute
      switch (control.type) {
        // 文本 - 单行多行
        case 2:
          attribute = { type: 'string' }
          break
        // 电话 - 手机
        case 3:
          attribute = { type: 'string' }
          break
        // 电话 - 座机
        case 4:
          attribute = { type: 'string' }
          break
        // 邮箱
        case 5:
          attribute = { type: 'string' }
          break
        // 数值
        case 6:
          attribute = { type: 'number' }
          break
        // 证件
        case 7:
          attribute = { type: 'string' }
          break
        // 金额
        case 8:
          attribute = { type: 'string' }
          break
        // 单选 - 平铺
        case 9:
          attribute = { type: 'string' }
          break
        // 多选 - 下拉
        case 10:
          attribute = { type: 'string' }
          break
        // 单选 - 下拉
        case 11:
          attribute = { type: 'string' }
          break
        // 附件
        case 14:
          attribute = { type: 'string' }
          break
        // 日期 - 年-月-日
        case 15:
          attribute = { type: 'datetime' }
          break
        // 日期 - 年-月-日 时:分
        case 16:
          attribute = { type: 'datetime' }
          break
        // 地区 - 省
        case 19:
          attribute = { type: 'string' }
          break
        // 自由连接
        case 21:
          attribute = { type: 'string' }
          break
        // 分段
        case 22:
          attribute = { type: 'string' }
          break
        // 地区 - 省/市
        case 23:
          attribute = { type: 'string' }
          break
        // 地区 - 省/市/县
        case 24:
          attribute = { type: 'string' }
          break
        // 大写金额
        case 25:
          attribute = { type: 'string' }
          break
        // 成员
        case 26:
          attribute = { type: 'json' }
          break
        // 部门
        case 27:
          attribute = { type: 'json' }
          break
        // 等级
        case 28:
          attribute = { type: 'string' }
          break
        // 关联记录
        case 29:
          attribute = {
            type: 'relation',
            relationType: 'manyWay',
            targetModel: `${mingdaoModelPrefix}${control.dataSource}`,
          }
          // 1 单选 2 多选
          if (control.enumDefault === 1) {
            attribute.maxItems = 1
          }
          if (control.sourceTitleControlId || control.showControls[0]) {
            attribute.mainField = control.sourceTitleControlId || control.showControls[0]
          }
          associations.push({
            alias: control.controlId,
            type: 'model',
            targetUid: `${mingdaoModelPrefix}${control.dataSource}`,
            model: control.dataSource,
            nature: 'manyWay',
            autoPopulate: true,
            dominant: true
          })
          break
        // 他表字段
        case 30:
          attribute = { type: 'string' }
          break
        // 公式 - 计算结果为数字
        case 31:
          attribute = { type: 'string' }
          break
        // 文本组合
        case 32:
          attribute = { type: 'string' }
          break
        // 自动编号
        case 33:
          attribute = { type: 'string' }
          break
        // 子表
        case 34:
          attribute = { type: 'string' }
          break
        // 级联选择
        case 35:
          attribute = { type: 'string' }
          break
        // 检查框
        case 36:
          attribute = { type: 'string' }
          break
        // 汇总
        case 37:
          attribute = { type: 'string' }
          break
        // 公式 - 计算结果为日期
        case 38:
          attribute = { type: 'date' }
          break
        // 定位
        case 40:
          attribute = { type: 'string' }
          break
        // 富文本
        case 41:
          attribute = { type: 'string' }
          break
        // 签名
        case 42:
          attribute = { type: 'string' }
          break
        // 嵌入
        case 45:
          attribute = { type: 'string' }
          break
        // 时间
        case 46:
          attribute = { type: 'datetime' }
          break
        // 条码
        case 47:
          attribute = { type: 'string' }
          break
        // 组织角色
        case 48:
          attribute = { type: 'string' }
          break
        // API查询
        case 49:
        case 50:
          attribute = { type: 'string' }
          break
        // 查询记录
        case 51:
          attribute = { type: 'string' }
          break
        // 备注
        case 10010:
          attribute = { type: 'string' }
          break
        default:
          attribute = { type: 'string' }
      }
      acc[control.controlId] = {
        label: control.controlName,
        ...attribute
      }
      return acc
    }, {}),
    allAttributes: [...customControls, ...systemControls, ...wfControls]
  }
  const mingdaoFields = {
    mingdao: worksheet,
    connection,
  }
  return {
    ...baseFields,
    associations: associations,
    ...mingdaoFields
  }
}

function mingdaoModelQuery (model) {
  const modelId = model.mingdao.id

  async function mingdaoFind (params) {
    const query = toMingdaoFilters(params)
    const data = await model.connection.getFilterRows({
      worksheetId: modelId,
      ...query,
    })
    return convertDataSource(data.rows, { model })
  }

  async function mingdaoCount (params) {
    const query = toMingdaoFilters(params)
    const data = await model.connection.getFilterRows({
      ...query,
      worksheetId: modelId,
      pageSize: 1,
      pageIndex: 1
    })
    return data.total
  }

  async function mingdaoCreate (values) {
    const controls = []
    for (let key of Object.keys(values)) {
      controls.push({
        controlId: key,
        value: values[key],
        valueType: 1,
      })
    }
    return await model.connection.addRow({
      worksheetId: modelId,
      triggerWorkflow: true,
      controls
    })
  }

  async function mingdaoUpdate (params, values) {
    const rowId = params.id
    const controls = []
    for (let key of Object.keys(values)) {
      const attribute = model.attributes[key]
      if (!attribute) continue
      if (attribute.type === 'relation') {
        controls.push({
          controlId: key,
          editType: 9,
          value: JSON.stringify(values[key])
        })
      } else {
        controls.push({
          controlId: key,
          value: values[key],
        })
      }
    }
    return await model.connection.editRow({
      worksheetId: modelId,
      rowId: rowId,
      triggerWorkflow: true,
      controls
    })
  }

  async function mingdaoFindOne (params) {
    const rowId = params.id
    const data = await model.connection.getRowByIdPost(modelId, rowId)
    return convertDataSource(data, { model })
  }

  async function mingdaoDeleteMany (params) {
    const rowId = params.id
    // TODO : Batch Delete
    return await model.connection.deleteRow({
      worksheetId: modelId,
      rowId: rowId
    })
  }

  return {
    mingdaoFind,
    mingdaoCount,
    mingdaoCreate,
    mingdaoUpdate,
    mingdaoFindOne,
    mingdaoDeleteMany,
    find: mingdaoFind,
    count: mingdaoCount,
    create: mingdaoCreate,
    update: mingdaoUpdate,
    findOne: mingdaoFindOne,
    deleteMany: mingdaoDeleteMany,
  }
}

function mingdaoModelInstance (model) {
  const modelQuery = mingdaoModelQuery(model)
  return Object.assign(modelQuery, model)
}

function toMingdaoFilters (params) {
  const filters = convertRestQueryParams(params)
  const mingdaoQuery = {}

  if (!isUndefined(filters.limit)) {
    mingdaoQuery.pageSize = filters.limit
  }

  if (!isUndefined(filters.start)) {
    mingdaoQuery.pageIndex = (filters.start / filters.limit) + 1
  }

  if (filters.where) {
    mingdaoQuery.filters = []
    for (let condition of filters.where) {
      const filter = {
        controlId: condition.field,
        spliceType: 1
      }
      filter.filterType = {
        'eq': 2,
        'ne': 6,
        'contains': 1,
        'ncontains': 5,
        'null': 7,
        'gt': 13,
        'gte': 14,
        'lt': 15,
        'lte': 16,
        'in': 24,
        'nin': 25,
      }[condition.operator] || 2
      if (Array.isArray(condition.value)) {
        filter.values = condition.value
      } else {
        filter.value = condition.value
      }
      mingdaoQuery.filters.push(filter)
    }
  }
  return mingdaoQuery
}

function convertDataSource (dataSource, options) {
  const { model } = options
  if (Array.isArray(dataSource)) {
    return dataSource.map(row => convertDataSource(row, options))
  }
  for (let key of Object.keys(dataSource)) {
    const attribute = model.attributes[key]
    if (!attribute) continue
    const rawData = dataSource[key]
    if (attribute.type === 'json' && rawData) {
      dataSource[key] = JSON.parse(rawData)
    }
    if (attribute.type === 'relation' && rawData) {
      const data = JSON.parse(rawData)
      if (Array.isArray(data)) {
        dataSource[key] = data.map(row => {
          if (isString(row)) return { id: row }
          const sourceValue = JSON.parse(row['sourcevalue'])
          return {
            ...sourceValue,
            id: sourceValue['rowid']
          }
        })
      }
    }
  }
  dataSource.id = dataSource['rowid']
  return dataSource
}

module.exports = {
  getAppSectionWorksheets,
  toMingdaoModel,
  toMingdaoFilters,
  convertDataSource,
  mingdaoModelQuery,
  mingdaoModelInstance,
}
