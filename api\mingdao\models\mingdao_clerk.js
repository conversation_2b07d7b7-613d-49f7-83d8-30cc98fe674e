'use strict';

module.exports = {
  "collectionName": "mingdao_clerk",
  "info": {
    "name": "mingdao_clerk",
    "label": "明道云用户类型配置",
    "description": "明道云用户类型配置"
  },
  "options": {
    "draftAndPublish": false,
    "timestamps": true
  },
  "pluginOptions": {},
  "attributes": {
    "name": {
      "label": "类型名称",
      "type": "string",
      required: true
    },
    account: {
      label: '登录明道云账号',
      type: 'string',
      visible: true,
      required: true

    },
    password: {
      label: '登录明道云密码',
      type: 'string',
      visible: true,
      required: true
    },
    "pBranch": {
      "label": "租户",
      "plugin": "users-permissions",
      "model": "branch",
      "configurable": false
    },
  }
}
