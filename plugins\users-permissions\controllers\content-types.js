'use strict'

const { validateKind } = require('./validation')
const {
  isNil, upperFirst,
  pick, has, prop, getOr, concat
} = require('lodash/fp')
const { keys } = require('lodash')
const pluralize = require('pluralize')
const { contentTypes: contentTypesUtils } = require('accel-utils')
const { getTimestampsAttributes, isMediaAttribute, isSingleType, } = require('accel-utils').contentTypes
const { PUBLISHED_AT_ATTRIBUTE } = require('accel-utils').contentTypes.constants

const formatContentTypeLabel = contentType => {
  const name = prop('info.name', contentType) || contentType.modelName
  if (contentType.info.label) return contentType.info.label
  try {
    return isSingleType(contentType)
      ? upperFirst(name)
      : upperFirst(pluralize(name))
  } catch (error) {
    // in case pluralize throws cyrillic characters
    return upperFirst(name)
  }
}

const formatAttributes = model => {
  return keys(model.attributes).reduce((acc, key) => {
    acc[key] = formatAttribute(key, model.attributes[key], { model })
    return acc
  }, {})
}

const formatAttribute = (key, attribute, { model }) => {
  // Normal Type
  if (has('type', attribute)
    && attribute.type !== 'relation'
    && attribute.type !== 'media'
  ) return attribute

  // Media Type
  if (isMediaAttribute(attribute)) {
    return {
      ...attribute,
      type: 'media',
      multiple: !!attribute.collection,
      required: !!attribute.required,
      allowedTypes: attribute.allowedTypes,
      pluginOptions: attribute.pluginOptions,
    }
  }

  // Relation Type
  const relation = (model.associations || []).find(assoc => assoc.alias === key)
  // Model Relation Main Field
  const shortTypes = ['string', 'number', 'integer', 'biginteger', 'float', 'decimal', 'datetime', 'uid']
  const relationModel = strapi.getModel(relation.targetUid)
  const defaultMainField = relationModel?.options.defaultMainField
  let firstNameKey
  for (let key in relationModel?.attributes) {
    if (key === 'id') continue
    if (shortTypes.includes(relationModel?.attributes[key].type || '')) {
      !firstNameKey && (firstNameKey = key)
    }
  }
  return {
    ...attribute,
    type: 'relation',
    targetModel: relation.targetUid,
    relationType: relation.nature,
    pluginOptions: attribute.pluginOptions,
    mainField: defaultMainField || attribute.mainField || firstNameKey || 'id'
  }
}

const isVisible = model => getOr(true, 'pluginOptions.content-manager.visible', model) === true

function toContentManagerModel (contentType) {
  let primaryAttributes
  // 临时扩展 primaryKey 不为 id 的情况
  // 用于支持外部数据库与模型
  if (contentType.orm === 'mongoose' && contentType.primaryKey !== '_id') {
    primaryAttributes = {
      [contentType.primaryKey]: {
        type: contentType.primaryKeyType,
      },
    }
  } else {
    primaryAttributes = {
      id: {
        type: contentType.primaryKeyType,
      },
    }
  }
  const formatedAttributes = formatAttributes(contentType)
  const timestampAttributes = getTimestampsAttributes(contentType)
  return {
    ...contentType,
    apiID: contentType.modelName,
    isDisplayed: isVisible(contentType),
    info: {
      ...contentType.info,
      label: formatContentTypeLabel(contentType),
    },
    attributes: {
      ...primaryAttributes,
      ...formatedAttributes,
      ...timestampAttributes,
    },
  }
}

function findContentType (id) {
  // uid match
  let contentType = strapi.getModel(id)
  return isNil(contentType) ? contentType : toContentManagerModel(contentType)
}

function findComponent (id) {
  // uid match
  let component = strapi.components[id]
  // modelName match
  if (!component && id !== '*') {
    for (let key in strapi.components) {
      const item = strapi.components[key]
      if (item?.modelName === id) {
        component = item
        break
      }
    }
  }
  return isNil(component) ? component : toContentManagerModel(component)
}

function getModel (id) {
  // Model - ContentType
  const contentType = findContentType(id)

  // Model - Component
  const component = findComponent(id)
  return contentType || component
}

const modelDtoFields = [
  'uid',
  'isDisplayed',
  'apiID',
  'kind',
  'category',
  'info',
  'options',
  'pluginOptions',
  'attributes',
  'pluginOptions',
  'primaryKey',
  'primaryKeyType',
]
const modelToDto = pick(modelDtoFields)

module.exports = {
  async findOneModel (ctx) {
    const { id } = ctx.params
    let model = getModel(id)

    // 明道云模型总是读取数据库
    if (strapi.plugins['mingdao'] && (!model || /^[0-9a-fA-F]{24}$/.test(id))) {
      const mingdaoPlugin = strapi.plugins['mingdao']
      if (mingdaoPlugin) {
        model = await mingdaoPlugin.services['mingdao-model'].mountMingdaoModelWithCache(id)
        model = toContentManagerModel(model)
      }
    }

    if (!model) {
      return ctx.notFound(`Model ${id} not found`)
    }
    ctx.body = modelToDto(model)
  },

  async findContentTypes (ctx) {
    // Model - ContentType
    const { kind } = ctx.query
    try {
      await validateKind(kind)
    } catch (error) {
      return ctx.send({ error }, 400)
    }
    let contentTypes = Object.values(strapi.contentTypes).map(toContentManagerModel)
    if (kind) {
      contentTypes = contentTypes.filter(contentTypesUtils.isKind(kind))
    }

    // Model - Component
    const components = Object.values(strapi.components).map(toContentManagerModel)
    const models = [
      ...contentTypes,
      ...components
    ]
    ctx.body = { data: models.map(modelToDto) }
  },

  async findRelations (ctx) {
    const { model: id, targetField } = ctx.params
    const { ...query } = ctx.request.query
    const { idsToOmit } = ctx.request.body

    if (query?._q === '') {
      delete query?._q
    }

    if (!targetField) {
      return ctx.badRequest()
    }

    let model = strapi.db.getModel(id)

    if (!model) {
      const mingdaoPlugin = strapi.plugins['mingdao']
      if (mingdaoPlugin) {
        model = await mingdaoPlugin.services['mingdao-model'].mountMingdaoModel(id)
      }
    }

    if (!model) {
      return ctx.notFound('model.notFound')
    }

    const attr = model.attributes[targetField]
    if (!attr) {
      return ctx.badRequest('targetField.invalid')
    }

    let target = strapi.db.getModelByAssoc(attr)

    if (!target) {
      const mingdaoPlugin = strapi.plugins['mingdao']
      if (mingdaoPlugin) {
        target = await mingdaoPlugin.services['mingdao-model'].mountMingdaoModel(attr.targetModel)
      }
    }

    if (!target) {
      return ctx.notFound('target.notFound')
    }

    if (idsToOmit && Array.isArray(idsToOmit)) {
      query._where = query._where || {}
      query._where.id_nin = concat(query._where.id_nin || [], idsToOmit)
    }

    // 增加租户过滤策略
    const branch = ctx.state.user.pBranch
    // 当前用户存在租户 && 当前数据模型存在 pBranch 字段
    if (branch && target.attributes.pBranch) {
      if (model.attributes.pBranch) {
        query.pBranch = branch.id
      }
    }

    const entityManager = strapi.plugins['users-permissions'].services['entity-manager']
    let entities = []

    if (has('_q', query)) {
      entities = await entityManager.search(query, target.uid)
    } else {
      entities = await entityManager.find(query, target.uid)
    }

    // 明道云 MainField 处理 - 临时方案
    // 优先级 1.abstract 2.name 3.mainField
    entities.forEach(e=>{
      e[attr.mainField] = e.abstract || e.name || e[attr.mainField]
    })

    if (!entities) {
      return ctx.notFound()
    }

    // 暂时禁用 pick fields - always false
    if (!ctx) {
      const pickFields = [attr.mainField, 'id', target.primaryKey, PUBLISHED_AT_ATTRIBUTE]
      ctx.body = entities.map(pick(pickFields))
    }

    ctx.body = entities
  },
}
