set -x
# 获取当前脚本所在的目录
script_dir=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
# 需要在************ 机器上执行该文件
# 不停明道云服务操作， 需要sudo 权限，外部调用脚本命令不需要sudo。 否则 scp 命令会需要密码
sudo docker exec $(sudo docker ps | grep community | awk '{print $1}') bash -c 'source /entrypoint.sh && backup mysql mongodb file'
# 进入备份目录
cd /data/mingdao/script/volume/data/
# 判断是否存在 backup 目录

if [ ! -d "backup" ]; then
  curl -X POST 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fd3de961-288c-4ec5-9fad-50046194df39' \
      -H 'Content-Type: application/json' \
      -d "{\"msgtype\": \"text\", \"text\": {\"content\": \"明道云备份失败，请检查服务器脚本\"}}"
  exit 1
fi

# 进入 backup 目录
cd backup

# 获取最新日期的压缩文件夹名称
backupName=$(ls -d */ | sed 's/\///g' | sort -r | head -n1)

# 判断backupName 不为空
if [ -z "$backupName" ]; then
  echo "No backup folder found."
  curl -X POST 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fd3de961-288c-4ec5-9fad-50046194df39' \
    -H 'Content-Type: application/json' \
    -d "{\"msgtype\": \"text\", \"text\": {\"content\": \"明道云备份失败，未找到有效文件，请检查服务器脚本\"}}"
  exit 1
fi

# 压缩目录
sudo tar -zcvf ./$backupName.tar.gz ./$backupName
# 获取文件大小
file_size=$(ls -lh ./$backupName.tar.gz | awk '{print $5}')

cd $script_dir

fileUrl=$(~/.local/node.js/current/bin/node uploadBos.js /data/mingdao/script/volume/data/backup $backupName.tar.gz)
exit_code=$?


if [ $exit_code -eq 0 ] && [ -n "$fileUrl" ]; then
  echo $fileUrl
  echo "Node.js script completed successfully."
  # 发送企信机器人提醒
  curl -X POST 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fd3de961-288c-4ec5-9fad-50046194df39' \
    -H 'Content-Type: application/json' \
    -d "{\"msgtype\": \"text\", \"text\": {\"content\": \"明道云已备份，\n文件地址:$fileUrl\n文件大小: $file_size\"}}"
  # 清理backup目录
  sudo rm -rf /data/mingdao/script/volume/data/backup
else
  echo "Node.js script encountered an error."
  # 发送企信机器人提醒
  curl -X POST 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fd3de961-288c-4ec5-9fad-50046194df39' \
    -H 'Content-Type: application/json' \
    -d "{\"msgtype\": \"text\", \"text\": {\"content\": \"明道云备份失败：文件上传出错\"}}"
fi
