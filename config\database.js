// 使用远程 mongo 数据库
module.exports = ({ env }) => {
  const dbConfig = {
    prod: {
      // host: '************',
      // port: 6010,
      // database: 'wly_mmd',
      // username: 'wly_write',
      // password: 'fefzmXkBbGllR057',
      uri: 'mongodb://wly_write:<EMAIL>:6010,n01.rs00.iyunxiao.com:6010,n02.rs00.iyunxiao.com:6010/wly_mmd?replicaSet=Replset00&readPreference=primary&slaveOk=true'
    },
    prodDev: {
      host: '************',
      port: 6010,
      database: 'wly_mmd',
      username: 'wly_write',
      password: 'fefzmXkBbGllR057',
    },
    test: {
      uri: 'mongodb://testwly_write:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testwly_mmd?replicaSet=ReplsetTest&readPreference=secondaryPreferred'
    },
    local: {
      // host: '*************',
      // port: 6010,
      // database: 'testwly-mmd',
      host: 'localhost',
      port: 27017,
      database: 'zhxy-dev',
      // host: '***********',
      // port: 6010,
      // database: 'testwly_mmd',
      // username: 'testwly_write',
      // password: 'ATAuEHQAQjzJu27p'
    },
  }[env('DATABASE', 'prod')]
  return {
    defaultConnection: 'default',
    connections: {
      default: {
        connector: 'mongoose',
        settings: {
          client: 'mongo',
          ...dbConfig
        },
        options: {
          authenticationDatabase: dbConfig.database,
          ssl: env('DATABASE_SSL', false),
          debug: env('DATABASE_DEBUG', false),
        },
      },
    },
  }
}
