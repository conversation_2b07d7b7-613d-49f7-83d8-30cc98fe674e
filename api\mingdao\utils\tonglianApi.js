const axios = require('axios')

async function request(path, params) {
  let config = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded' // 设置请求头，确保服务器正确解析
    },
    timeout: 10000,
    method: 'post',
    url: `https://vsp.allinpay.com/apiweb/${path}`,
    data: params
  }
  let result = await axios.request(config)
  return result.data
}

async function pay(params) {
  return await request('unitorder/pay', params)
}

async function refund(params) {
  return await request('tranx/refund', params)
}

async function query(params) {
  return await request('tranx/query', params)
}

module.exports = {
  pay,
  refund,
  query,
}
