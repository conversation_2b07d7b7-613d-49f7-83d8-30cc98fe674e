const mingdaoOrder = require('./order')
const mingdaoAfterSale = require('./after_sale')
const wechatPay = require('./wechat_pay')
const tonglianPay = require('./tonglian_pay')
const { getWechatAuthData } = require('strapi-plugin-users-permissions/utils/wechat')
const mingdaoApi = require('../utils/mingdaoApi')

async function createPayOrder(ctx) {
  let { orderNo, appId, configId } = ctx.request.body

  // 多租户支付为不影响线上美美哒使用 兼容处理
  // TODO 后续删除
  if (!configId || configId === 'undefined') {
    configId = '663f0d852a31b6deeacb2c6e'
  }

  if (!orderNo || !appId || !configId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  const mingdaoConfig = await mingdaoApi.getMingdaoConfig(configId);

  // 订单信息获取
  const order = await mingdaoOrder.getDetailByOrderNo(orderNo, mingdaoConfig.pBranch.id)
  if (!order) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '订单不存在')
  }

  if (order.orderStatus !== '待支付') {
    return ctx.wrapper.error('PARAMETERS_ERROR', '订单状态异常，无法发起支付')
  }

  const payType = order.payType || '通联支付'

  // 用户信息获取
  const userId = ctx.state.user.id
  const userService = strapi.plugins['users-permissions'].services.user
  const user = await userService.findOne({ id: userId })
  const thirdParty = user.thirdParties?.find(e => e.openid)
  if (!thirdParty) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '账号小程序 OpenId 匹配异常')
  }
  const { openid, app } = await strapi.components['account.wechat-app'].findOne({
    app: appId,
    account: userId
  })

  if (!app) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'APP ID 错误')
  }

  // 优先使用订单支付金额
  let amountPrice = parseInt((parseFloat(order.payAmount) * 100).toFixed()) || 0
  if (amountPrice <= 0) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '订单金额异常，无法发起支付')
  }

  if (payType === '微信支付') {
    let payChannel = null;
    switch (app.type) {
      case 'wechatMiniProgram': payChannel = 0; break;
      case 'wechatOfficialAccount': payChannel = 1; break;
    }
    return await wechatPay.createPayOrder(ctx, mingdaoConfig, orderNo, amountPrice, openid, payChannel)
  } else if (payType === '通联支付') {
    // W01	微信扫码支付 W02	微信JS支付 W06	微信小程序支付
    let tonglianPayType = null;
    switch (app.type) {
      case 'wechatMiniProgram': tonglianPayType = 'W06'; break;
      case 'wechatOfficialAccount': tonglianPayType = 'W02'; break;
    }
    if (!tonglianPayType) {
      return ctx.wrapper.error('PARAMETERS_ERROR', '支付类型异常，无法发起支付')
    }
    return await tonglianPay.createPayOrder(ctx, mingdaoConfig, orderNo, amountPrice, openid, app.verifyData.appid, tonglianPayType)
  } else {
    return ctx.wrapper.error('PARAMETERS_ERROR', '支付类型异常，无法发起支付')
  }
}

async function getH5PayParams(ctx) {
  let { orderNo, appId, returl, configId } = ctx.request.body

  // 多租户支付为不影响线上美美哒使用 兼容处理
  // TODO 后续删除
  if (!configId || configId === 'undefined') {
    configId = '663f0d852a31b6deeacb2c6e'
  }

  if (!orderNo || !appId || !returl || !configId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  const mingdaoConfig = await mingdaoApi.getMingdaoConfig(configId);

  // 订单信息获取
  const order = await mingdaoOrder.getDetailByOrderNo(orderNo, mingdaoConfig.pBranch.id)

  if (!order) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '订单不存在')
  }

  if (order.orderStatus !== '待支付') {
    return ctx.wrapper.error('PARAMETERS_ERROR', '订单状态异常，无法发起支付')
  }

  const payType = order.payType || '通联支付'

  // 优先使用订单支付金额
  let amountPrice = parseInt((parseFloat(order.payAmount) * 100).toFixed()) || 0
  if (amountPrice <= 0) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '订单金额异常，无法发起支付')
  }

  if (payType === '微信支付') {
    return ctx.wrapper.error('PARAMETERS_ERROR', '支付方式异常，无法发起支付')
  } else if (payType === '通联支付') {
    return await tonglianPay.getH5PayParams(ctx, mingdaoConfig, orderNo, amountPrice, returl)
  } else {
    return ctx.wrapper.error('PARAMETERS_ERROR', '支付类型异常，无法发起支付')
  }
}

async function refund(ctx) {
  const { transaction_id, refund_id, amount, reason } = ctx.request.body
  console.log('body ', transaction_id, refund_id, amount, reason)
  if (!transaction_id || !refund_id || !amount) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  // 优先使用订单支付金额
  let amountPrice = parseInt((parseFloat(amount) * 100).toFixed()) || 0
  if (amountPrice <= 0) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '金额异常，无法发起退款')
  }

  const order = await strapi.services['mingdao_order'].findOne({ transaction_id: transaction_id })

  if (!order) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '交易订单不存在，无法发起退款')
  }

  const mingdaoConfig = await mingdaoApi.getMingdaoConfig(null, order.pBranch.id);

  // 订单信息获取
  const afterSale = await mingdaoAfterSale.getDetailByAfterSaleNo(refund_id, order.pBranch.id)
  if (!afterSale) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '售后订单不存在')
  }
  // if (afterSale.refundStatus !== '退款中') {
  //   return ctx.wrapper.error( 'PARAMETERS_ERROR', '退款状态异常，无法发起退款')
  // }


  const refundRecords = await strapi.services['mingdao_refund'].find({ pBranch: order.pBranch.id, third_refund_no: refund_id, refund_status: { $in: [0, 1, 3] } })
  if (refundRecords && refundRecords.length) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '正在退款中或退款已完成，无法重复发起退款')
  }

  let refundReason = reason || refund_id

  if (order.payment_type === 0) { // 微信支付
    return await wechatPay.refund(ctx, mingdaoConfig, order, refund_id, amountPrice, refundReason)
  } else if (order.payment_type === 2) { // 通联支付
    return await tonglianPay.refund(ctx, mingdaoConfig, order, refund_id, amountPrice, refundReason)
  } else {
    return ctx.wrapper.error('PARAMETERS_ERROR', '订单支付方式异常，无法发起退款')
  }
}

async function wxPay(ctx) {
  const { appId, configId, userId, amountPrice } = ctx.request.body

  if (!appId || !configId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  const userInfo = await strapi.query('mingdao_user').findOne({ id: userId })
  if (!userInfo) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '用户不存在')
  }

  const mingdaoConfig = await wechatPay.getConfigPay(configId);
  if (!mingdaoConfig) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '支付配置不存在')
  }

  // 用户信息获取
  const account = ctx.state.user.id

  const { openid, app } = await strapi.components['account.wechat-app'].findOne({
    app: appId,
    account: account
  })

  const result = await wechatPay.wxVipPay(ctx, mingdaoConfig, openid, userInfo.mingdao_configs)
  console.info("🚀 ~ wxPay ~ result:", result)

  await strapi.services['vip_order'].create({
    userName: userInfo.name,
    order_no: result.order_no,
    mingdao_usertype: userInfo.mingdao_usertype,
    mingdao_configs: userInfo.mingdao_configs,
    paymentAmount: amountPrice,
    paymentNumber: result.package,
    paymentStatus: 0,
    pBranch: userInfo.pBranch,
  })

  return result


}

module.exports = {
  createPayOrder,
  getH5PayParams,
  refund,
  wxPay
}
