const sdk = require('@baiducloud/sdk')
const path = require('path')
const BosClient = sdk.BosClient

const config = {
  endpoint: 'https://ayx-wly.bj.bcebos.com',         //传入Bucket所在区域域名
  credentials: {
    ak: 'ALTAKOVxs9pURgBgK3dBp8LAhG',         //您的AccessKey
    sk: '46f036aa332447f7801008cec77fc107'     //您的SecretAccessKey
  }
}

const fileDir = process.argv[2]
const fileName = process.argv[3]

async function main () {
  let fileUrl = await uploadFile(fileDir, fileName)
  console.log(fileUrl)
  process.exit(0) // 成功完成后退出
}

main().catch((err) => {
  console.error('Error:', err)
  process.exit(1) // 遇到错误时退出
})

async function uploadFile (dir, name) {
  // console.log('https://wly-oss.yunxiao.com/' + 'prod/mmd/backup/' + name)

  const file = path.resolve(dir, name)

  let key = 'prod/mmd/backup/' + name
  let client = new BosClient(config)

  try {
    let response = await client.putObjectFromFile(null, key, file, {
      'x-bce-acl': 'public-read'
    })
    // console.log(response)
    return 'https://wly-oss.yunxiao.com/' + key
  } catch (e) {
    console.error(e)
    process.exit(1) // 遇到错误时退出
  }
}
