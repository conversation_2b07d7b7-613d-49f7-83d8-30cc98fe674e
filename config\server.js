module.exports = ({ env }) => {

  const port = env.int('NODE_PORT', 3015)

  const serverConfig = {
    prod: {
      serverUrl: `https://wly-mmd-api-lan.iyuxiao.com`,
      serverWanUrl: `https://wly-mmd-api-wan.iyunxiao.com`,
      adminUrl: `http://wly-mmd.iyunxiao.com`,
      crmUrl: `https://boss-crm.iyunxiao.com/wly/contract`,
      boosWfUrl: `http://boss-wf.iyunxiao.com`,
      huobanApiUrl: `https://api.huoban.com/openapi`,
      configUrl: `https://wly-config-api.iyunxiao.com`,
      proxyOptions: {
        '/meimeida': {
          target: 'http://app.meimeida.net/',
          changeOrigin: true,
          cookieDomainRewrite: 'app.meimeida.net',
          pathRewrite: {
            '/meimeida/': '/', // rewrite path
          }
        }
      }
    },
    test: {
      serverUrl: `https://testwly-mmd-api.iyunxiao.com`,
      serverWanUrl: `https://testwly-mmd-api-wan.iyunxiao.com`,
      adminUrl: `http://localzhxy-dev.yunxiao.com:${port + 1}`,
      crmUrl: `https://testboss-crm.iyunxiao.com/wly/contract`,
      boosWfUrl: `http://testboss-wf.iyunxiao.com`,
      huobanApiUrl: `https://api.huoban.com/openapi`,
      configUrl: `https://wly-config-api.iyunxiao.com`,
      proxyOptions: {
        '/meimeida': {
          target: 'http://app.meimeida.net/',
          changeOrigin: true,
          cookieDomainRewrite: 'app.meimeida.net',
          pathRewrite: {
            '/meimeida/': '/', // rewrite path
          }
        }
      }
    },
    local: {
      serverUrl: `https://wly-mmd-api.iyunxiao.com`,
      serverWanUrl: `https://wly-mmd-api-wan.iyunxiao.com`,
      adminUrl: `http://localzhxy-dev.yunxiao.com:${port + 1}`,
      crmUrl: `https://testboss-crm.iyunxiao.com/wly/contract`,
      boosWfUrl: `http://testboss-wf.iyunxiao.com`,
      huobanApiUrl: `https://api.huoban.com/openapi`,
      configUrl: `http://wly-config-api.iyunxiao.com`,
      proxyOptions: {
        '/meimeida': {
          target: 'http://app.meimeida.net/',
          changeOrigin: true,
          cookieDomainRewrite: 'app.meimeida.net',
          pathRewrite: {
            '/meimeida/': '/', // rewrite path
          }
        }
      }
    },
  }[env('SERVER', 'prod')]

  const webhookConfig = {
    // api报错时的webhook
    prod: {
      webhookUrl: '',
    },
    test: {
      webhookUrl: '',
    },
    local: {
      webhookUrl: '',
    },
  }[env('SERVER', 'prod')]

  return {
    host: env('HOST', '0.0.0.0'),
    port: port,
    serverUrl: serverConfig.serverUrl,
    serverWanUrl: serverConfig.serverWanUrl,
    adminUrl: serverConfig.adminUrl,
    webhookUrl: webhookConfig.webhookUrl,
    resetPasswordUrl: serverConfig.resetPasswordUrl,
    crmUrl: serverConfig.crmUrl,
    boosWfUrl: serverConfig.boosWfUrl,
    proxyOptions: serverConfig.proxyOptions,
    huoban: {
      url: serverConfig.huobanApiUrl,
      'Open-Authorization': 'Bearer EzenEYv2W5F0R7DXzSLrZxMwP1teBrsiaSEPCosl',
    },
    config: {
      url: serverConfig.configUrl,
      accessKey: 'JYHcBwIewmqZ4dxdEHgdDrpl'
    },
    admin: {
      serveAdminPanel: false,
      auth: {
        secret: env('ADMIN_JWT_SECRET', 'Rq0JX4TiAH5ECBUpyLGf'),
      },
    },
    jwt: {
      expiresIn: 3600 * 24 * 30
    },
    // 小程序相关配置
    wechat: {
      appid: '',
      secret: '',
      token: ''
    },
    // 短信相关配置 - 当前支持腾讯云短信
    sms: {
      secretId: '',
      secretKey: '',
      region: '',
      SmsSdkAppId: '',
      SignName: ''
    },
    // 验证码相关配置 - 当前支持腾讯云滑动验证码
    captcha: {
      secretId: '',
      secretKey: '',
      CaptchaAppId: 2072317168,
      AppSecretKey: ''
    }
  }
}
