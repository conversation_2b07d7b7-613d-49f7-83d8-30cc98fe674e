'use strict'
const lodash = require('lodash')

const {
  passwordBcrypt, passwordCompare, jwtGen, jwtVerify, createRandomToken
} = require('accel-utils')

const { sendSmsCode } = require('../utils/sms')
const { ObjectId } = require('mongodb')
const { parseJumpToken } = require('../utils/wechat')
const { genRandomString } = require('../utils/utils')

const pluginConfig = strapi.config.get('plugins.usersPermissions') || { branchMode: 'normal' }

async function clearAccountCode (account) {
  const iPlugin = strapi.plugins['users-permissions']
  const userService = iPlugin.services['user']
  return await userService.update({
    id: account.id,
  }, {
    loginCode: null,
    loginCodeTime: null,
    loginCodeTryCount: 0,
  })
}

/**
 * 注册账号
 * @param ctx
 * @param {object} ctx.request.body
 * @param {string} ctx.request.body.username
 * @param {string} ctx.request.body.phone
 * @param {string} ctx.request.body.password
 * @param {*} ctx.wrapper
 */
async function register (ctx) {
  const iPlugin = strapi.plugins['users-permissions']
  let data = ctx.request.body
  let { username, phone, email, password, appId } = data
  const app = appId && await strapi.query('app', 'users-permissions').findOne({ id: appId })
  if (!app) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'APP ID 错误')
  }
  if (!username) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '用户名不可为空')
  }
  const passwordRegExp = /[\w\d]{8,64}/
  if (!passwordRegExp.test(password)) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '密码格式错误')
  }
  if (phone) {
    const phoneRegExp = /^((13[0-9])|(14[5-9])|(15([0-3]|[5-9]))|(16[6-7])|(17[1-8])|(18[0-9])|(19[1|3])|(19[5|6])|(19[8|9]))\d{8}$/
    if (!phoneRegExp.test(phone || '')) {
      return ctx.wrapper.error('PARAMETERS_ERROR', '手机号码格式错误')
    }
    let account = await iPlugin.services['user'].findOne({ phone })
    if (account) {
      return ctx.wrapper.error('HANDLE_ERROR', '此手机号码已注册')
    }
  }
  if (email) {
    let matchUser = await iPlugin.services['user'].findOne({ email })
    if (matchUser) {
      return ctx.wrapper.error('HANDLE_ERROR', '邮箱已注册')
    }
  }
  if (!email && !phone) {
    return ctx.wrapper.error('HANDLE_ERROR', '账号格式错误')
  }
  let encryptedPassword = await passwordBcrypt(password)
  const user = await iPlugin.services['user'].createNewUser(ctx, {
    username: username,
    phone: phone,
    email: email,
    password: encryptedPassword,
    confirmed: true,
  })
  return ctx.wrapper.succ(iPlugin.services['user'].getFullAuthData(user))
}

/**
 * 注册账号
 * @param ctx
 * @param {object} ctx.request.body
 * @param {string} ctx.request.body.phone
 * @param {string} ctx.request.body.password
 * @param {*} ctx.wrapper
 */
async function loginByAccount (ctx) {
  const iPlugin = strapi.plugins['users-permissions']
  let data = ctx.request.body
  const appId = data.appId
  const account = data.account
  const password = data.password
  const app = appId && await strapi.query('app', 'users-permissions').findOne({ id: appId })
  if (!app) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'APP ID 错误')
  }
  if (!account) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '手机号码账号不可为空')
  }
  if (!password) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '密码不可为空')
  }
  let phoneUser, emailUser, accountUser
  if (account === '<EMAIL>') {
    phoneUser = await iPlugin.services['user'].findOne({ phone: account })
    emailUser = await iPlugin.services['user'].findOne({ email: account })
  } else if (account && pluginConfig.branchMode === 'domain') {
    const branch = await iPlugin.services['branch'].getCtxDomainBranch(ctx)
    phoneUser = await iPlugin.services['user'].findOne({ phone: account, pBranch: branch.id })
    emailUser = await iPlugin.services['user'].findOne({ email: account, pBranch: branch.id })
  } else if (account) {
    phoneUser = await iPlugin.services['user'].findOne({ phone: account })
    emailUser = await iPlugin.services['user'].findOne({ email: account })
    accountUser = await iPlugin.services['user'].findOne({ account: account })
  }
  accountUser = phoneUser || emailUser || accountUser
  // 密码校验
  const retryTimeLimit = 1000 * 60 * 10
  const retryTimeInterval = new Date() - (accountUser.loginPasswordTryTime || 0)
  let loginPasswordTryCount = accountUser.loginPasswordTryCount || 0
  // 10分钟内超过三次锁定尝试
  if (retryTimeInterval <= retryTimeLimit && loginPasswordTryCount > 3) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '账号密码多次输入错误，请十分钟后再尝试')
  }
  // 10分钟后重置尝试次数
  if (retryTimeInterval > retryTimeLimit && loginPasswordTryCount > 3) {
    await iPlugin.services['user'].update({ id: accountUser.id }, {
      loginPasswordTryTime: new Date(),
      loginPasswordTryCount: 0,
    })
  }
  if (!accountUser || !await passwordCompare(password, accountUser.password)) {
    // 匹配用户且密码错误 - 记录重试次数
    if (accountUser) {
      await iPlugin.services['user'].update({ id: accountUser.id }, {
        loginPasswordTryTime: new Date(),
        loginPasswordTryCount: loginPasswordTryCount + 1,
      })
    }
    return ctx.wrapper.error('PARAMETERS_ERROR', '账号或密码错误')
  }
  return ctx.wrapper.succ(iPlugin.services['user'].getFullAuthData(accountUser))
}

/**
 * wiki:<link>https://wiki.iyunxiao.com/pages/viewpage.action?pageId=683357</link>
 * 如【业务平台登录】接口为 http://testboss.iyunxiao.com/v1/sessions
 * 【扫码登录服务】在完成扫码验证后将回调：http://testboss.iyunxiao.com/v1/sessions?id=boss&token=xxxxx&go=xxx&userid=xxx
 * 则回调函数得到4个参数：id（appid）、token、go、userid
 *
 *  1.解析 token：解析方式可以找管理员获取；
 * const userInfo = Utils.parseJumpToken(Config.get('loginSecret'), token);
 * userInfo 中的信息为：[name, departmentId, position, userid]
 *
 * 2. 根据 userInfo 判断权限，若未通过则报错，通过后在业务系统内种下 cookie；
 * 3. 根据 go 跳转至用户访问页面（注意：如果 go 参数为 “/”，则重定向到前端根目录下）；
 * @param ctx
 * @returns {Promise<*>}
 */
async function loginByYxWeCom (ctx) {
  let { id: appId, token, userid } = ctx.request.body

  // 1、参数校验，为空
  if (!appId || !token) {
    return ctx.wrapper.error('AUTH_ERROR', 'id 和 token 不可为空')
  }

  const iPlugin = strapi.plugins['users-permissions']
  const app = appId && await strapi.query('app', 'users-permissions').findOne({ sId: appId })
  if (!app) {
    return ctx.wrapper.error('AUTH_ERROR', 'APP ID 错误')
  }

  const secret = app.tokenSecret

  // 2、解析 token
  const userInfo = parseJumpToken(secret, token, 5)
  if (!userInfo || !userInfo.length) {
    return ctx.wrapper.error('AUTH_ERROR', 'token 解析失败')
  }
  let wxUser = {
    username: userInfo[0], // 用户名称
    departmentId: userInfo[1], // 用户所在部门的企业微信 ID
    description: userInfo[2], // 用户职位
    id: userInfo[3], // 用户的企业微信 ID
  }
  if (userid !== wxUser.id) {
    return ctx.wrapper.error('AUTH_ERROR', 'userid 不匹配')
  }

  let user = await iPlugin.services['user'].findOne({ customId: wxUser.id })
  const branch = await iPlugin.services['branch'].getCtxDefaultBranch()
  if (!user) {
    // 用户不存在，先创建一个
    user = await iPlugin.services['user'].createNewUser(ctx, {
      username: wxUser.username,
      customId: wxUser.id,
      pBranch: branch.id,
      provider: 'yxWeCom'
    }, null)
  }

  return ctx.wrapper.succ(iPlugin.services['user'].getFullAuthData(user))
}

async function _decodeToken ({ appId, token }) {
  const app = appId && await strapi.query('app', 'users-permissions').findOne({ id: appId })
  if (!app) {
    throw Error('APP ID 错误')
  }
  return await jwtVerify(token, app.tokenSecret)
}

async function checkToken (ctx) {
  const iPlugin = strapi.plugins['users-permissions']
  const { appId, token } = ctx.request.body
  try {
    const app = appId && await strapi.query('app', 'users-permissions').findOne({ id: appId })
    if (!app) {
      return ctx.wrapper.error('PARAMETERS_ERROR', 'APP ID 错误')
    }
    const decodedToken = await _decodeToken({ appId, token })
    const accountId = decodedToken.data.id
    let user = await iPlugin.services['user'].findOne({ id: accountId })
    return ctx.wrapper.succ(iPlugin.services['user'].getFullAuthData(user))
  } catch (e) {
    ctx.wrapper.error('HANDLE_ERROR', e)
  }
}

async function getAppAuthToken (ctx) {
  const iPlugin = strapi.plugins['users-permissions']
  const { appId, token, authAppId } = ctx.request.body
  const tokenData = await _decodeToken({ appId, token })
  const account = tokenData.data
  const app = authAppId &&
    await strapi.query('app', 'users-permissions').findOne({ id: authAppId })
  if (!app) {
    return ctx.wrapper.error('HANDLE_ERROR', e)
  }
  return ctx.wrapper.succ({
    jwt: jwtGen({
      id: account.id,
      username: account.username,
      phone: account.phone,
      email: account.email,
    }, app.tokenSecret),
    user: account,
  })
}

// 超级管理员平台运维使用
async function superQuickLogin (ctx) {
  const iPlugin = strapi.plugins['users-permissions']
  const { id } = ctx.request.body
  let user = id === 'SuperAdmin'
    ? await iPlugin.services['user'].findOne({ email: '<EMAIL>' })
    : await iPlugin.services['user'].findOne({ id })
  if (!user) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '用户不存在')
  }
  return ctx.wrapper.succ(iPlugin.services['user'].getFullAuthData(user))
}

async function forgotPasswordByEmail (ctx) {
  const iPlugin = strapi.plugins['users-permissions']
  const { email } = ctx.request.body

  const emailError = () => ctx.wrapper.error('PARAMETERS_ERROR', 'Email 错误')
  if (!email) return emailError()

  const account = await iPlugin.services['user'].findOne({ email })
  if (!account) return emailError()

  const resetPasswordToken = createRandomToken()
  await iPlugin.services['user'].update({ id: account.id, }, { resetPasswordToken })
  const url = `${ctx.request.header.origin}/reset-password?code=${resetPasswordToken}`
  const sendResult = await strapi.plugins.email.services.email.sendTemplatedEmail(
    {
      to: email,
      from: strapi.config.get('server.admin.forgotPassword.from'),
      replyTo: strapi.config.get('server.admin.forgotPassword.replyTo'),
    },
    strapi.config.get('server.admin.forgotPassword.emailTemplate'),
    {
      url,
      user: lodash.pick(account, ['email', 'firstname', 'lastname', 'username']),
    },
  )
  return ctx.wrapper.succ(sendResult)
}

async function getUserByResetPasswordCode (ctx) {
  const iPlugin = strapi.plugins['users-permissions']
  const { code } = ctx.query
  if (!code) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '密码重置链接错误或已失效')
  }
  const user = await iPlugin.services.user.findOne({ resetPasswordToken: code })
  if (!user) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '密码重置链接错误或已失效')
  }
  delete user.password
  return ctx.wrapper.succ(user)
}

async function resetPasswordByCode (ctx) {
  const iPlugin = strapi.plugins['users-permissions']
  const { password, code } = ctx.request.body
  if (!code) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '密码重置链接错误或已失效')
  }
  const user = await iPlugin.services.user.findOne({ resetPasswordToken: code })
  if (!user) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '密码重置链接错误或已失效')
  }
  const hashPassword = await passwordBcrypt(password)
  const result = await iPlugin.services.user.update({
    resetPasswordToken: code
  }, {
    resetPasswordToken: null,
    password: hashPassword
  })
  return ctx.wrapper.succ(result)
}

function _genCode (length) {
  return ('' + Math.random()).slice(2, length + 2)
}

const CODE_VALID_TIME = 60 * 1000
const MAX_RETRY_TIME = 2

function isCodeExpire (loginCodeTime) {
  return new Date() - loginCodeTime > CODE_VALID_TIME
}

async function sendLoginCode (ctx) {
  const iPlugin = strapi.plugins['users-permissions']
  const userService = iPlugin.services['user']

  const code = _genCode(4)

  // let { ticket, randStr } = ctx.request.body
  // try {
  //   // 腾讯云要 1000 块钱起购 - 所以先不验证了、假装通过
  //   if (!code) {
  //     await verifyCaptcha(ticket, randStr, ctx._ip)
  //   }
  //   if (!ticket) {
  //     throw new Error('ticket error')
  //   }
  // } catch (e) {
  //   return ctx.wrapper.error('PARAMETERS_ERROR', `图形验证码失败 ${e}`)
  // }

  let { phone, pBranch } = ctx.request.body
  if (!/1\d{10}/.test(phone)) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'Phone 手机号码错误')
  }
  let user = await userService.findOne({ phone: phone })
  // 已经存在账号
  if (user) {
    // if (user.confirmed) {
    //   return ctx.wrapper.error('HANDLE_ERROR', '此手机号码已注册, 请直接扫码登录！')
    // }
    // 已发送验证码
    if (user.loginCode && user.loginCodeTime) {
      // 验证码未过期
      if (new Date() - user.loginCodeTime < CODE_VALID_TIME) {
        return ctx.wrapper.error('HANDLE_ERROR', '已发送的验证码未失效')
      }
      // 清除过期验证码
      await clearAccountCode(user)
    }
    // 发送验证码
    await userService.update({
      id: user.id,
    }, {
      loginCode: code,
      loginCodeTime: new Date(),
      loginCodeTryCount: 0,
    })
    await sendSmsCode(phone, code)
    return ctx.wrapper.succ('验证码发送成功')
  }
  const role = await strapi.query('role', 'users-permissions').findOne({ type: 'branch-user' }, [])
  // 不存在账号则自动创建
  await userService.createNewUser(ctx, {
    username: '试用账户',
    phone: phone,
    // 新账号未激活
    confirmed: false,
    loginCode: code,
    loginCodeTime: new Date(),
    loginCodeTryCount: 0,
    // 租户与角色
    pBranch: pBranch,
    role: role.id
  })
  await sendSmsCode(phone, code)
  return ctx.wrapper.succ('验证码发送成功')
}

async function loginByPhoneAndCode (ctx) {
  const iPlugin = strapi.plugins['users-permissions']
  const userService = iPlugin.services['user']
  let { phone, code, appId } = ctx.request.body
  const app = appId && await strapi.query('app', 'users-permissions').findOne({ id: appId })
  if (!app) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'APP ID 错误')
  }
  if (!/1\d{10}/.test(phone)) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'Phone 手机号码错误')
  }
  let user = await userService.findOne({ phone: phone })
  // 不存在验证码
  if (!user || !user.loginCode) {
    return ctx.wrapper.error('HANDLE_ERROR', '验证码未发送或已失效')
  }
  // 验证码失效
  if (user.loginCodeTryCount >= MAX_RETRY_TIME) {
    return ctx.wrapper.error('HANDLE_ERROR', '验证码由于多次重试已失效')
  }
  // 验证码过期
  if (isCodeExpire(user.loginCodeTime)) {
    return ctx.wrapper.error('HANDLE_ERROR', '验证码已过期')
  }
  // 验证码错误 - 记录重试次数
  if (user.loginCode !== code) {
    await userService.update({
      id: user.id,
    }, {
      loginCodeTryCount: user.loginCodeTryCount + 1,
    })
    return ctx.wrapper.error('HANDLE_ERROR', '验证码错误')
  }
  // 验证码正确 - 如果未激活则激活账号
  if (!user.confirmed) {
    await userService.update({
      id: user.id,
    }, {
      confirmed: true,
    })
  }
  await clearAccountCode(user)
  return ctx.wrapper.succ(iPlugin.services['user'].getFullAuthData(user))
}

function _userAddNewBranch (user, branch, role) {
  const newBranchId = branch['id']
  return {
    role: role.id,
    roles: [role.id],
    pBranch: newBranchId,
    pBranches: [
      ...user.pBranches.map(e => e.id),
      newBranchId,
    ],
    pBranchConfigs: [
      ...user.pBranchConfigs,
      {
        branchId: newBranchId,
        role: role.id,
        roles: [role.id],
        blocked: false,
        username: user.username,
        phone: user.phone,
        email: user.email,
      }
    ]
  }
}

// 用户创建新租户
async function createNewBranch (ctx) {
  const { name, shortName } = ctx.request.body
  const user = ctx.state.user
  // 新账号首先创建默认租户或使用参数传递的租户
  const branch = await strapi.query('branch', 'users-permissions').create(
    {
      name: name,
      shortName: shortName,
      type: ObjectId().toString(),
    }
  )
  const iPlugin = strapi.plugins['users-permissions']
  const defaultRole = await iPlugin.services['user'].getDefaultRole()
  const userBranchUpdate = _userAddNewBranch(user, branch, defaultRole)
  await strapi.query('user', 'users-permissions').update({
    id: user.id
  }, userBranchUpdate)
  return branch
}

// 租户邀请Token生成
async function createBranchInviteToken (ctx) {
  const user = ctx.state.user
  const { inviteTokenExpiredAt } = ctx.request.body
  const inviteToken = ObjectId().toString() + genRandomString(24)
  // 默认两天后过期
  const defaultExpiredAt = new Date(+new Date() + 1000 * 3600 * 24 * 2)
  await strapi.query('branch', 'users-permissions').update(
    { id: user.pBranch.id },
    {
      inviteToken: inviteToken,
      inviteTokenExpiredAt: inviteTokenExpiredAt || defaultExpiredAt
    }
  )
  return {
    inviteToken,
  }
}

// 加入租户
async function joinBranchByToken (ctx) {
  const { inviteToken } = ctx.request.body
  const user = ctx.state.user
  const branch = await strapi.query('branch', 'users-permissions').findOne(
    { inviteToken: inviteToken }
    , []
  )
  if (!branch) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '邀请Token无效')
  }
  if (branch.inviteTokenExpiredAt < new Date()) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '邀请Token已过期')
  }
  const matchBranch = user.pBranches.find(e => e.id === branch.id)
  if (matchBranch) {
    return ctx.wrapper.error('HANDLE_ERROR', '已经加入该组织')
  }
  const role = await strapi.query('role', 'users-permissions').findOne(
    { type: 'branch-user' },
    []
  )
  const userBranchUpdate = _userAddNewBranch(user, branch, role)
  await strapi.query('user', 'users-permissions').update({
    id: user.id
  }, userBranchUpdate)
  return ctx.wrapper.succ('加入成功')
}

// 使用 TokenKey 登录
async function loginByTokenKey (ctx) {
  const { tokenKey } = ctx.request.body
  const token = await strapi.query('token', 'users-permissions').findOne({
    key: tokenKey
  })
  const user = token.user
  if (!user || !token.expireAt) {
    return ctx.wrapper.error('HANDLE_ERROR', 'Token 无效')
  }
  const iPlugin = strapi.plugins['users-permissions']
  const expiresIn = Math.floor((token.expireAt - new Date()) / 1000)
  if (expiresIn <= 0) {
    return ctx.wrapper.error('HANDLE_ERROR', 'Token 已过期')
  }
  const fullAuthData = iPlugin.services['user'].getFullAuthData(user, {
    tokenId: token.id,
  }, {
    expiresIn: expiresIn
  })
  return {
    code: 0,
    msg: 'OK',
    data: fullAuthData
  }
}

module.exports = {
  register,
  loginByAccount,
  loginByYxWeCom,
  checkToken,
  getAppAuthToken,
  superQuickLogin,
  loginByTokenKey,
  // 密码重置
  forgotPasswordByEmail,
  getUserByResetPasswordCode,
  resetPasswordByCode,
  // 手机验证码登录
  sendLoginCode,
  loginByPhoneAndCode,
  // 租户操作
  createNewBranch,
  joinBranchByToken,
  createBranchInviteToken,
}
