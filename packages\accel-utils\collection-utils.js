const { parseMultipartData } = require('./lib')
const { passwordBcrypt } = require('./verify')
const _ = require('lodash')
const ExcelJS = require('exceljs')

function getModelService (model) {
  return {
    find ({ params, populate }) {
      return strapi.entityService.find({ params, populate }, { model: model })
    },
    search ({ params, populate }) {
      return strapi.entityService.search({ params, populate }, { model: model })
    },
    count ({ params, populate }) {
      return strapi.entityService.count({ params, populate }, { model: model })
    },
    countSearch ({ params, populate }) {
      return strapi.entityService.countSearch({ params, populate }, { model: model })
    },
    findOne ({ params, populate }) {
      return strapi.entityService.findOne({ params, populate }, { model: model })
    },
    create ({ data, files }) {
      return strapi.entityService.create({ data, files }, { model: model })
    },
    update ({ params, data, files }) {
      if (!params.id) {
        throw Error('Multiple updates are prohibited.')
      }
      return strapi.entityService.update({ params, data, files }, { model: model })
    },
    delete ({ params }) {
      if (!params.id) {
        throw Error('Multiple deletions are prohibited.')
      }
      return strapi.entityService.delete({ params }, { model: model })
    },
    deleteMany ({ params }) {
      if (!_.isArray(params.id_in)) {
        throw Error('Delete many params.ids invalid.')
      }
      return strapi.query(model).delete(params)
    },
  }
}

function parseCtxData (ctx) {
  // Params
  const params = { ...ctx.params }

  // 移除在无路径参数时附加的路径 params 参数
  // Example: { 0: 'branch/cards/count' }
  if (params) {
    delete params[0]
    delete params[1]
    delete params[2]
    delete params[3]
    delete params[4]
  }

  // Model
  // model eg. plugins::users-permissions.view
  // model eg. account
  const model = params.model
  delete params.model

  // Query
  const query = ctx.query
  let data, files
  if (ctx.is && ctx.is('multipart') && ctx.request.body.data) {
    const parseData = parseMultipartData(ctx)
    data = parseData.data
    files = parseData.files
  } else {
    data = ctx.request.body
  }

  // Populate
  if (ctx.query._populate) {
    ctx.populate = ctx.query._populate
    delete ctx.query._populate
  }
  const populate = ctx.populate || undefined

  return {
    model, params, query, data, files, populate: populate
  }
}

async function parseCtxDomainAndBranch (ctx, modelName, pluginName) {
  const { model, params, query, data, files, populate } = parseCtxData(ctx)
  const strapiModel = strapi.getModel(modelName || model, pluginName)
  const iPlugin = strapi.plugins['users-permissions']
  const branch = await iPlugin.services['branch'].getCtxDomainBranch(ctx)
  const branchId = branch?.id
  let branchQuery = query
  let branchParams = params
  // 租户数据过滤机制 - 无租户不过滤 [平台超级管理员用户]
  if (branchId) {
    if (strapiModel.attributes.pBranches) {
      branchQuery = {
        pBranches: branchId
      }
    } else {
      branchQuery = {
        pBranch: branchId
      }
    }
  }
  if (query) {
    branchQuery = {
      ...query,
      ...branchQuery
    }
  }
  if (params) {
    branchQuery = {
      ..._.omit(params, ['model']),
      ...branchQuery
    }
    branchParams = {
      ..._.omit(params, ['model']),
      ...branchQuery
    }
  }
  return {
    model: strapiModel,
    params: branchParams,
    query: branchQuery,
    data, files,
    populate,
    branchId
  }
}

function parseCtxUserAndBranch (ctx, modelName) {
  const { model, params, query, data, files, populate } = parseCtxData(ctx)
  const strapiModel = strapi.getModel(modelName || model)
  let user = ctx.state.user
  if (!user) throw Error('未登录状态无接口权限')

  const branchId = user.pBranch?.id
  let branchQuery = query
  let branchParams = params
  // 租户数据过滤机制 - 无租户不过滤 [平台超级管理员用户]
  if (branchId) {
    if (strapiModel.attributes.pBranches) {
      branchQuery = {
        pBranch: branchId
        // TODO 先使用临时方案，这样写会导致需组合其他查询字段的情况有问题
        // _or: [
        //   { pBranches: branchId },
        //   { pBranch: branchId },
        // ]
      }
    } else {
      branchQuery = {
        pBranch: branchId
      }
    }
  }
  if (query) {
    branchQuery = {
      ...query,
      ...branchQuery
    }
  }
  if (params) {
    branchQuery = {
      ..._.omit(params, ['model']),
      ...branchQuery
    }
    branchParams = {
      ..._.omit(params, ['model']),
      ...branchQuery
    }
  }
  return {
    model: strapiModel,
    params: branchParams,
    query: branchQuery,
    data, files,
    populate,
    user,
    branchId
  }
}

async function dataFieldProcess (data, model) {
  // 密码类型字段处理
  const attributes = model.attributes
  for (let key of Object.keys(attributes)) {
    if (attributes[key].type === 'password') {
      if (data[key]) {
        const passwordRegExp = /[\w\d]{8,64}/
        if (!passwordRegExp.test(data.password)) {
          throw Error('密码格式错误，需要为8位及以上字母或数字')
        }
        data[key] = await passwordBcrypt(data.password)
      } else {
        delete data[key]
      }
    }
  }
}

// 导出模型数据为 Excel
function modelDataExport (model, entities, { includeAttributes } = {}) {
  const { htmlToText } = require('html-to-text');

  // 生成 Excel
  const modelName = model.info.label || model.info.name
  const workbook = new ExcelJS.Workbook()
  workbook.creator = 'iTixiao'
  workbook.lastModifiedBy = 'iTixiao'
  const worksheet = workbook.addWorksheet(
    modelName,
    { properties: {} }
  )
  const columns = Object.keys(model.attributes).filter(attrKey => {
    if (includeAttributes) return includeAttributes.includes(attrKey)
    return true
  }).map(key => {
    const attr = model.attributes[key]
    return {
      header: attr.label || attr.name,
      key: key,
      width: 24,
      _attr: attr
    }
  })
  worksheet.columns = includeAttributes
    ? includeAttributes.map(attrKey => {
      return columns.find(column => column.key === attrKey)
    })
    : columns
  // worksheet.columns = [
  //   { header: '标题', key: 'title', width: 48 },
  //   { header: '作者', key: 'author', width: 20 },
  //   { header: '创建人', key: 'creator', width: 10 },
  //   { header: '内容', key: 'content', width: 128 }
  // ]
  for (const doc of entities) {
    const row = {}
    for (let column of columns) {
      const attr = column._attr
      if (attr.type === 'enumeration' && attr.options) {
        const option = attr.options.find(e => e.value === doc[column.key])
        if (option) {
          doc[column.key] = option.label
        }
      } else if (attr.type === 'richtext' ){
        if (doc[column.key]) {
        // 获取解析后的富文本内容
          doc[column.key] = htmlToText(doc[column.key]);
        }
      }
    }
    // TODO 优先展示名称而不是ID后，无法将数据重新导入
    for (let key of Object.keys(doc)) {
      // 针对关系字段优先展示关联模型的名称字段
      if (model.attributes[key]?.model || model.attributes[key]?.collection) {
        if (_.isArray(doc[key])) {
          row[key] = doc[key].map(e => {
            if (model.attributes[key].mainField) {
              return e[model.attributes[key].mainField]
            } else if (e.name || e.title) {
              return e.name || e.title
            } else {
              return e.id || e
            }
          })
        } else if (_.isObject(doc[key])) {
          if (model.attributes[key].mainField) {
            row[key] = doc[key][model.attributes[key].mainField]
          } else if (doc[key].name || doc[key].title) {
            row[key] = doc[key].name || doc[key].title
          } else {
            row[key] = doc[key].id
          }
        } else {
          row[key] = doc[key]
        }
      }
      // options 字段展示 label
      else if (model.attributes[key]?.options) {
        const option = model.attributes[key].options.find(e => e.value === doc[key])
        if (option) {
          row[key] = option.label
        } else {
          row[key] = doc[key]
        }
      }
      // JSON 字段展示名称
      else if (model.attributes[key]?.type === 'json') {
        const getType = param => {
          const type = Object.prototype.toString.call(param)
          return type.slice(8, -1)
        }
        const type = getType(doc[key])
        if (type === 'Array') {
          row[key] = `JSON ${type} ${doc[key].length}`
        }
      } else {
        row[key] = doc[key]
      }
    }
    worksheet.addRow(row)
  }
  return workbook
}

const hasDraftAndPublish = model => _.get(model, 'options.draftAndPublish', false) === true

module.exports = {
  parseCtxData,
  parseCtxUserAndBranch,
  parseCtxDomainAndBranch,
  dataFieldProcess,
  getModelService,
  hasDraftAndPublish,
  modelDataExport,
}
