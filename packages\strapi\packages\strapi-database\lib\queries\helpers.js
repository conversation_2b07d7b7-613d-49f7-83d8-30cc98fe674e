'use strict';

const _ = require('lodash');
const { executeBeforeLifecycle, executeAfterLifecycle } = require('../utils/lifecycles');

/**
 * If exists, rename the key "id" by the primary key name of the model ("_id" by default for mongoose).
 */
const replaceIdByPrimaryKey = (params, model) => {
  const newParams = { ...params };
  if (_.has(params, 'id')) {
    delete newParams.id;
    newParams[model.primaryKey] = params[model.primaryKey] || params.id;
  }
  return newParams;
};

const withLifecycles = ({ query, model, fn }) => async (params, ...rest) => {
  // substitute id for primaryKey value in params
  const newParams = replaceIdByPrimaryKey(params, model);
  const queryArguments = [newParams, ...rest];

  // execute before hook
  await executeBeforeLifecycle(query, model, ...queryArguments);

  // execute query
  const result = await fn(...queryArguments);

  // execute after hook with result and arguments
  await executeAfterLifecycle(query, model, result, ...queryArguments);

  // return result
  return result;
};

// wraps a connectorQuery call with:
// - param substitution
// - lifecycle hooks
const createQueryWithLifecycles = ({ query, model, connectorQuery }) => {
  return withLifecycles({
    query,
    model,
    fn: (...queryParameters) => connectorQuery[query](...queryParameters),
  });
};

module.exports = { withLifecycles, createQueryWithLifecycles };
