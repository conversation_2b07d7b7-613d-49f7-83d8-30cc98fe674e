// 列表查询
interface MingdaoApiFilterRowParams {
  worksheetId: string; // 工作表ID
  pageSize?: number;   // 行数
  pageIndex?: number;  // 页码
  viewId?: string;     // 视图ID,可为空
  sortId?: string;     // 排序字段ID
  isAsc?: boolean;     // 是否升序
  listType?: number;   // 返回数据类型（不传默认为0，推荐填入1）
                       // 0：按指定视图的数据格式返回（非表格视图则获取数据的数量会有所限制）
                       // 1：按表格视图的数据格式返回（可以获取所有数据）。

  filters?: MingdaoFilter[]; // 筛选器
  controls?: string[];       // 指定控件列表（ID或别名），默认返回全部字段

  notGetTotal?: boolean;      // 是否不统计总行数以提高性能
  useControlId?: boolean;     // 是否只返回controlId，默认false
  getSystemControl?: boolean; // 是否获取系统字段，默认false
}

// 筛选器
interface MingdaoFilter {
  controlId: string;                  // 字段ID
  dataType: MingdaoDateRangeEnum;     // 控件类型编号，枚举值 MingdaoDateRangeEnum
  spliceType: 1 | 2;                  // 拼接方式，1:And 2:Or
  filterType: MingdaoFilterTypeEnum;  // 筛选类型，枚举值 MingdaoFilterTypeEnum
  values?: (string | MingdaoAccountID)[];    // 根据筛选类型，传多个值，传特殊 MingdaoAccountID
  value?: string | MingdaoAccountID;         // 根据筛选类型，传单个值，传特殊 MingdaoAccountID

  dateRange?: number,     // 日期范围 DateRangeEnum
  dateRangeType?: number, // 日期范围类型，1：天 2：周 3：月 4：季 5：年

  minValue?: string,      // 最小值
  maxValue?: string,      // 最大值
  isAsc?: boolean,        // 是否升序 false:降序

  isGroup?: boolean,      // 当前筛选条件是否是筛选组，配合groupFilters 参数，支持单层筛选组
  groupFilters?: MingdaoFilter[],  // 筛选组
}

// user-self 当前用户 user-sub 下属 user-workflow 工作流 user-api API
type MingdaoAccountID = 'user-self' | 'user-sub' | 'user-workflow' | 'user-api'

declare enum MingdaoDateRangeEnum {
  Default = 0,          // 默认
  Today = 1,            // 今天
  Yesterday = 2,        // 昨天
  Tomorrow = 3,         // 明天
  ThisWeek = 4,         // 本周
  LastWeek = 5,         // 上周
  NextWeek = 6,         // 下周
  ThisMonth = 7,        // 本月
  LastMonth = 8,        // 上月
  NextMonth = 9,        // 下月
  LastEnum = 10,        // 上..
  NextEnum = 11,        // 下..
  ThisQuarter = 12,     // 本季度
  LastQuarter = 13,     // 上季度
  NextQuarter = 14,     // 下季度
  ThisYear = 15,        // 本年
  LastYear = 16,        // 去年
  NextYear = 17,        // 明年
  Customize = 18,       // 自定义
  Last7Day = 21,        // 过去7天
  Last14Day = 22,       // 过去14天
  Last30Day = 23,       // 过去30天
  Next7Day = 31,        // 未来7天
  Next14Day = 32,       // 未来14天
  Next33Day = 33        // 未来33天
}

declare enum MingdaoFilterTypeEnum {
  Default = 0,          // 默认
  Like = 1,             // 包含
  Eq = 2,               // 是（等于）
  Start = 3,            // 开头为
  End = 4,              // 结尾为
  NContain = 5,         // 不包含
  Ne = 6,               // 不是（不等于）
  IsNull = 7,           // 为空
  HasValue = 8,         // 不为空
  Between = 11,         // 在范围内
  NBetween = 12,        // 不在范围内
  Gt = 13,              // >
  Gte = 14,             // >=
  Lt = 15,              // <
  Lte = 16,             // <=
  DateEnum = 17,        // 日期是
  NDateEnum = 18,       // 日期不是
  MySelf = 21,          // 我拥有的
  UnRead = 22,          // 未读
  Sub = 23,             // 下属
  RCEq = 24,            // 关联控件是
  RCNe = 25,            // 关联控件不是
  ArrEq = 26,           // 数组等于
  ArrNe = 27,           // 数组不等于
  DateBetween = 31,     // 在范围内
  DateNBetween = 32,    // 不在范围内
  DateGt = 33,          // >
  DateGte = 34,         // >=
  DateLt = 35,          // <
  DateLte = 36,         // <=
  NormalUser = 41,      // 常规用户
  PortalUser = 42       // 外部门户用户
}

// 新增记录
interface MingdaoApiControl {
  controlId: string,        // 控件ID
  value: string             // 控件值
  valueType?: 1 | 2,         // 提交值类型，默认1
  // 1=外部文件链接，放在 value 参数中
  // 2=文件流字节编码 base64 格式字符串，文件流 base64 信息放在 controlFiles 参数中
  controlFiles?: {
    baseFile: string,        // base64字符串（文件流字节编码）
    fileName: string,        // 文件名称，带后缀
  }[]
}

interface MingdaoApiAddRowParams {
  worksheetId: string;              // 工作表ID
  controls: MingdaoApiControl[];    // 控件数组
  triggerWorkflow: boolean,         // 触发工作流
}

interface MingdaoApiEditRowParams {
  worksheetId: string;              // 工作表ID
  rowId: string;                    // 行记录ID
  controls: MingdaoApiControl & {
    editType?: 0 | 1,               // 数据更新类型，0=覆盖，1=新增（默认0:覆盖，新建记录可不传该参数）
  }[];    // 控件数组
  triggerWorkflow: boolean,         // 触发工作流
}

interface MingdaoApiDeleteRowParams {
  worksheetId: string;              // 工作表ID
  rowId: string;                    // 行记录ID
  triggerWorkflow: boolean,         // 触发工作流
}
