'use strict';

module.exports = {
  "collectionName": "vip_order",
  "info": {
    "name": "vip_order",
    "label": "明道云用户类型配置",
    "description": "明道云用户类型配置"
  },
  "options": {
    "draftAndPublish": false,
    "timestamps": true
  },
  "pluginOptions": {},
  "attributes": {
    "userName": {
      "label": "登录名",
      "type": "string",
      required: true
    },
    order_no: {
      label: '订单号',
      type: 'string',
      visible: true
    },
    transaction_id: {
      "label": "交易单号",
      "type": "string",
      "editable": false
    },
    mingdao_usertype: {
      label: '用户类型',
      model: 'mingdao_usertype',
      visible: true,
      required: true,
    },
    mingdao_configs: {
      label: '应用配置',
      model: 'mingdao_config',
      visible: true,
      required: true,
    },
    paymentNumber: {
      label: '付款编号',
      type: 'string',
      visible: true
    },
    paymentAmount: {
      label: '付款金额',
      type: 'number',
      visible: true
    },
    paymentTime: {
      label: '付款时间',
      type: 'datetime',
      visible: true
    },
    paymentStatus: {
      label: '付款状态',
      type: 'number',
      visible: true,
      options: [
        { label: '未付款', value: 0 },
        { label: '已付款', value: 1 },
        { label: '已退款', value: 2 },
      ]
    },
    payInfo: {
      "label": "支付信息",
      "type": "json"
    },
    "pBranch": {
      "label": "租户",
      "plugin": "users-permissions",
      "model": "branch",
      "configurable": false
    },
  }
}
