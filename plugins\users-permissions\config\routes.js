const { createDefaultRoutes } = require('accel-utils')
const userPermissionRoutes = [
  {
    'method': 'GET',
    'path': '/',
    'handler': 'UsersPermissions.index',
    'config': {
      'policies': []
    }
  },
  {
    'method': 'GET',
    'path': '/search/:id',
    'handler': 'UsersPermissions.searchUsers',
    'config': {
      'policies': [],
      'description': 'Search for users',
      'tag': {
        'plugin': 'users-permissions',
        'name': 'User',
        'actionType': 'find'
      }
    }
  },
  {
    'method': 'GET',
    'path': '/policies',
    'handler': 'UsersPermissions.getPolicies',
    'config': {
      'policies': []
    }
  },
  {
    'method': 'GET',
    'path': '/roles/:id',
    'handler': 'UsersPermissions.getRole',
    'config': {
      'policies': [],
      'description': 'Retrieve a role depending on its id',
      'tag': {
        'plugin': 'users-permissions',
        'name': 'Role',
        'actionType': 'findOne'
      }
    }
  },
  {
    'method': 'GET',
    'path': '/roles',
    'handler': 'UsersPermissions.getRoles',
    'config': {
      'policies': [],
      'description': 'Retrieve all role documents',
      'tag': {
        'plugin': 'users-permissions',
        'name': 'Role',
        'actionType': 'find'
      }
    }
  },
  {
    'method': 'GET',
    'path': '/routes',
    'handler': 'UsersPermissions.getRoutes',
    'config': {
      'policies': []
    }
  },
  {
    'method': 'GET',
    'path': '/advanced',
    'handler': 'UsersPermissions.getAdvancedSettings',
    'config': {
      'policies': []
    }
  },
  {
    'method': 'PUT',
    'path': '/advanced',
    'handler': 'UsersPermissions.updateAdvancedSettings',
    'config': {
      'policies': []
    }
  },
  {
    'method': 'GET',
    'path': '/permissions',
    'handler': 'UsersPermissions.getPermissions',
    'config': {
      'policies': []
    }
  },
  {
    'method': 'POST',
    'path': '/roles',
    'handler': 'UsersPermissions.createRole',
    'config': {
      'policies': [],
      'description': 'Create a new role',
      'tag': {
        'plugin': 'users-permissions',
        'name': 'Role',
        'actionType': 'create'
      }
    }
  },
  {
    'method': 'PUT',
    'path': '/roles/:role',
    'handler': 'UsersPermissions.updateRole',
    'config': {
      'policies': [],
      'description': 'Update a role',
      'tag': {
        'plugin': 'users-permissions',
        'name': 'Role',
        'actionType': 'update'
      }
    }
  },
  {
    'method': 'DELETE',
    'path': '/roles/:role',
    'handler': 'UsersPermissions.deleteRole',
    'config': {
      'policies': [],
      'description': 'Delete a role',
      'tag': {
        'plugin': 'users-permissions',
        'name': 'Role',
        'actionType': 'destroy'
      }
    }
  },
]

const userRoutes = [
  {
    path: '/users/me',
    method: 'GET',
    handler: 'User.me',
    config: {
      policies: [],
      prefix: '',
      description: 'Retrieve the logged in user information',
      tag: {
        plugin: 'users-permissions',
        name: 'User',
        actionType: 'findOne'
      }
    }
  },
  {
    path: '/users/me',
    method: 'PUT',
    handler: 'User.updateMe',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    path: '/users/me/roles',
    method: 'PUT',
    handler: 'User.changeCurrentRole',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    path: '/users/me/branches',
    method: 'PUT',
    handler: 'User.changeCurrentBranch',
    config: {
      policies: [],
      prefix: ''
    }
  },
  // 用户管理
  ...createDefaultRoutes({
    controller: 'User',
    basePath: '/users',
    config: {
      'policies': [], 'prefix': '',
    },
  }),
  // 租户用户管理
  ...createDefaultRoutes({
    controller: 'User',
    basePath: '/branch/users',
    config: {
      'policies': [], 'prefix': '',
    },
    mode: 'branch'
  }),
  // 域名租户用户管理
  ...createDefaultRoutes({
    controller: 'User',
    basePath: '/domain/users',
    config: {
      'policies': [], 'prefix': '',
    },
    mode: 'domain'
  }),
]

const groupRoutes = [
  ...createDefaultRoutes({
    controller: 'group',
    basePath: '/groups',
    config: {
      'policies': [],
    },
  }),
]

const pageRoutes = [
  ...createDefaultRoutes({
    controller: 'page',
    basePath: '/pages',
    config: {
      'policies': [],
    },
  }),
]

const branchRoutes = [
  // fetchDomainBranchInfo
  {
    method: 'GET',
    path: '/branches/info',
    handler: 'branch.fetchDomainBranchInfo',
    config: {
      policies: [],
      prefix: '',
    }
  },
  {
    method: 'GET',
    path: '/branches/current',
    handler: 'branch.fetchCurrentBranch',
    config: {
      policies: [],
      prefix: '',
    }
  },
  {
    method: 'PUT',
    path: '/branches/current',
    handler: 'branch.updateCurrentBranch',
    config: {
      policies: [],
      prefix: '',
    }
  },
  ...createDefaultRoutes({
    controller: 'branch',
    basePath: '/branches',
    config: {
      'policies': [],
    },
  }),
]

const collectionRoutes = [
  // Collection
  {
    path: '/collections/:model',
    method: 'GET',
    handler: 'collection.find',
    config: {
      policies: []
    }
  },
  {
    path: '/collections/:model/count',
    method: 'GET',
    handler: 'collection.count',
    config: {
      policies: []
    }
  },
  {
    path: '/collections/:model/:id',
    method: 'GET',
    handler: 'collection.findOne',
    config: {
      policies: []
    }
  },
  {
    path: '/collections/:model',
    method: 'POST',
    handler: 'collection.create',
    config: {
      policies: []
    }
  },
  {
    path: '/collections/:model/:id',
    method: 'PUT',
    handler: 'collection.update',
    config: {
      policies: []
    }
  },
  {
    path: '/collections/:model/:id',
    method: 'DELETE',
    handler: 'collection.delete',
    config: {
      policies: []
    }
  },
  // Collection User Filter
  {
    path: '/user-collections/:model',
    method: 'GET',
    handler: 'collection-user.find',
    config: {
      policies: []
    }
  },
  {
    path: '/user-collections/:model/count',
    method: 'GET',
    handler: 'collection-user.count',
    config: {
      policies: []
    }
  },
  {
    path: '/user-collections/:model/export',
    method: 'GET',
    handler: 'collection-user.export',
    config: {
      policies: []
    }
  },
  {
    path: '/user-collections/:model/:id',
    method: 'GET',
    handler: 'collection-user.findOne',
    config: {
      policies: []
    }
  },
  {
    path: '/user-collections/:model',
    method: 'POST',
    handler: 'collection-user.create',
    config: {
      policies: []
    }
  },
  {
    path: '/user-collections/:model/:id',
    method: 'PUT',
    handler: 'collection-user.update',
    config: {
      policies: []
    }
  },
  {
    path: '/user-collections/:model/:id',
    method: 'DELETE',
    handler: 'collection-user.delete',
    config: {
      policies: []
    }
  }
]

const contentTypeRoutes = [
  // 0.2.0
  {
    method: 'GET',
    path: '/models/:id',
    handler: 'content-types.findOneModel',
    config: {
      policies: []
    }
  },
  {
    method: 'POST',
    path: '/relations/:model/:targetField',
    handler: 'content-types.findRelations',
    config: {
      policies: []
    }
  },
  {
    method: 'GET',
    path: '/content-types',
    handler: 'content-types.findContentTypes',
    config: {
      policies: []
    }
  }
]

const menuRoutes = [
  {
    method: 'GET',
    path: '/menus/base',
    handler: 'menu.getBaseMenu',
    config: {
      policies: []
    }
  },
  {
    method: 'PUT',
    path: '/menus/base',
    handler: 'menu.setBaseMenu',
    config: {
      policies: []
    }
  },
  ...createDefaultRoutes({
    controller: 'menu',
    basePath: '/menus',
    config: {
      'policies': [],
    },
  }),
]

const appRoutes = [
  {
    method: 'GET',
    path: '/apps',
    handler: 'app.find',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'GET',
    path: '/apps/count',
    handler: 'app.count',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'POST',
    path: '/apps',
    handler: 'app.create',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'PUT',
    path: '/apps/:id',
    handler: 'app.update',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'DELETE',
    path: '/apps/:id',
    handler: 'app.delete',
    config: {
      policies: [],
      prefix: ''
    }
  }
]

const authRoutes = [
  {
    method: 'POST',
    path: '/auth/local/register',
    handler: 'Auth.register',
    config: {
      policies: [
        'plugins::users-permissions.ratelimit'
      ],
      prefix: '',
      description: 'Register a new user with the default role',
      tag: {
        plugin: 'users-permissions',
        name: 'User',
        actionType: 'create'
      }
    }
  },
  {
    method: 'POST',
    path: '/auth/forgot-password',
    handler: 'Auth.forgotPassword',
    config: {
      policies: [
        'plugins::users-permissions.ratelimit'
      ],
      prefix: '',
      description: 'Send the reset password email link',
      tag: {
        plugin: 'users-permissions',
        name: 'User'
      }
    }
  },
  {
    method: 'POST',
    path: '/auth/reset-password',
    handler: 'Auth.resetPassword',
    config: {
      policies: [
        'plugins::users-permissions.ratelimit'
      ],
      prefix: '',
      description: 'Reset user password with a code (resetToken)',
      tag: {
        plugin: 'users-permissions',
        name: 'User'
      }
    }
  },
  {
    method: 'GET',
    path: '/auth/email-confirmation',
    handler: 'Auth.emailConfirmation',
    config: {
      policies: [],
      prefix: '',
      description: 'Validate a user account',
      tag: {
        plugin: 'users-permissions',
        name: 'User'
      }
    }
  },
  {
    method: 'POST',
    path: '/auth/send-email-confirmation',
    handler: 'Auth.sendEmailConfirmation',
    config: {
      policies: [],
      prefix: '',
      description: 'Send a confirmation email to user',
      tag: {
        plugin: 'users-permissions',
        name: 'User'
      }
    }
  },
]

const authExtendRoutes = [
  {
    method: 'POST',
    path: '/uc/register',
    handler: 'auth-extend.register',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'POST',
    path: '/uc/loginByAccount',
    handler: 'auth-extend.loginByAccount',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'POST',
    path: '/uc/loginByYxWeCom',
    handler: 'auth-extend.loginByYxWeCom',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'POST',
    path: '/uc/loginByYxWeCom',
    handler: 'auth-extend.loginByYxWeCom',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'POST',
    path: '/uc/checkToken',
    handler: 'auth-extend.checkToken',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'POST',
    path: '/uc/getAppAuthToken',
    handler: 'auth-extend.getAppAuthToken',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'POST',
    path: '/uc/forgetPasswordByEmail',
    handler: 'auth-extend.forgotPasswordByEmail',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'GET',
    path: '/accounts/actions/getUserByResetPasswordCode',
    handler: 'auth-extend.getUserByResetPasswordCode',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'PUT',
    path: '/accounts/actions/resetPasswordByCode',
    handler: 'auth-extend.resetPasswordByCode',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'POST',
    path: '/uc/sendLoginCode',
    handler: 'auth-extend.sendLoginCode',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'POST',
    path: '/uc/loginByPhoneAndCode',
    handler: 'auth-extend.loginByPhoneAndCode',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'POST',
    path: '/uc/superQuickLogin',
    handler: 'auth-extend.superQuickLogin',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'POST',
    path: '/uc/createNewBranch',
    handler: 'auth-extend.createNewBranch',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'POST',
    path: '/uc/createBranchInviteToken',
    handler: 'auth-extend.createBranchInviteToken',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'POST',
    path: '/uc/joinBranchByToken',
    handler: 'auth-extend.joinBranchByToken',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    method: 'POST',
    path: '/uc/loginByTokenKey',
    handler: 'auth-extend.loginByTokenKey',
    config: {
      policies: [],
      prefix: ''
    }
  },
]

const authWechat = [
  {
    path: '/oauth/wechat',
    method: 'POST',
    handler: 'auth-wechat.loginByMiniprogram',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    path: '/oauth/wechatOfficial',
    method: 'POST',
    handler: 'auth-wechat.loginByOfficialAccount',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    path: '/oauth/wechatPhoneNumber',
    method: 'POST',
    handler: 'auth-wechat.bindMiniprogramPhoneNumber',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    path: '/oauth/miniprogramUrlLink',
    method: 'GET',
    handler: 'auth-wechat.getMiniprogramUrlLink',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    path: '/oauth/miniprogramQRCode',
    method: 'GET',
    handler: 'auth-wechat.getMiniprogramQRCode',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    path: '/oauth/miniprogramACode',
    method: 'GET',
    handler: 'auth-wechat.getMiniprogramACode',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    path: '/uc/QRCodeTicket',
    method: 'GET',
    handler: 'auth-wechat.getOffiaccountQRCodeTicket',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    path: '/uc/checkLogin',
    method: 'POST',
    handler: 'auth-wechat.checkOffiaccountLogin',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    path: '/wechat/event',
    method: 'GET',
    handler: 'auth-wechat.verifyOffiaccountEvent',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    path: '/wechat/event',
    method: 'POST',
    handler: 'auth-wechat.handleOffiaccountEvent',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    path: '/wechat/action/getUserOffiaccountInfo',
    method: 'POST',
    handler: 'auth-wechat.getUserOffiaccountInfo',
    config: {
      policies: [],
      prefix: ''
    }
  },
  {
    path: '/wechat/action/getOffiaccountSignature',
    method: 'GET',
    handler: 'auth-wechat.getOffiaccountSignature',
    config: {
      policies: [],
      prefix: ''
    }
  }
]

const tokenRoutes = [
  ...createDefaultRoutes({
    controller: 'token',
    basePath: '/tokens',
    config: {
      policies: [],
    },
  }),
]

module.exports = {
  'routes': [
    ...userPermissionRoutes,
    ...authRoutes,
    ...userRoutes,
    ...groupRoutes,
    ...pageRoutes,
    ...branchRoutes,
    ...collectionRoutes,
    ...contentTypeRoutes,
    ...menuRoutes,
    ...authExtendRoutes,
    ...authWechat,
    ...appRoutes,
    ...tokenRoutes,
  ]
}
