'use strict'
/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#core-controllers)
 * to customize this controller
 */
const { CurdRouter } = require('accel-utils')

const curdRouter = new CurdRouter('group', { pluginName: 'users-permissions' })

module.exports = {
  ...curdRouter.createHandlers(),
  async update (ctx) {
    const {
      params: { id },
      request: { body }
    } = ctx
    const userPermissions = await strapi.query('group', 'users-permissions').update({ id }, body)
    // 更新功能模块关联的角色的接口权限
    for (let role of userPermissions.roles) {
      await strapi.plugins['users-permissions'].services.userspermissions.updateRole(
        role.id,
        {
          modules: role.modules
        }
      )
    }
    return userPermissions
  },
}
