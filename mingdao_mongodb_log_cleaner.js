#!/usr/bin/env node

const { MongoClient } = require('mongodb')

// 数据库连接配置
const DB_CONFIGS = {
  actionlog: {
    url: "mongodb://127.0.0.1:6010/mdservicedata",
    dbName: "mdservicedata"
  },
  wslog: {
    url: "mongodb://127.0.0.1:6010/mdworksheetlog",
    dbName: "mdworksheetlog"
  },
  workflow: {
    url: "mongodb://127.0.0.1:6010/mdworkflow",
    dbName: "mdworkflow"
  }
}

// 日志配置
const LOG_LEVELS = {
  INFO: 'INFO',
  WARN: 'WARN',
  ERROR: 'ERROR'
}

// 日志函数
function log (level, message, data = null) {
  const timestamp = new Date().toISOString()
  const logMessage = `[${timestamp}] [${level}] ${message}`
  console.log(logMessage)
  if (data) {
    console.log(JSON.stringify(data, null, 2))
  }
}

// 获取三个月前的日期
function getThreeMonthsAgo () {
  const date = new Date()
  date.setMonth(date.getMonth() - 3)
  return date
}

// 获取三个月前的年月字符串 (YYYYMM格式)
function getThreeMonthsAgoYearMonth () {
  const date = getThreeMonthsAgo()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  return `${year}${month}`
}

// 连接数据库
async function connectDB (config) {
  try {
    const client = new MongoClient(config.url, {
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000,
      connectTimeoutMS: 10000,
    })
    await client.connect()
    log(LOG_LEVELS.INFO, `成功连接到数据库: ${config.dbName}`)
    return client
  } catch (error) {
    log(LOG_LEVELS.ERROR, `连接数据库失败: ${config.dbName}`, error)
    throw error
  }
}

// 分析 al_actionlog* 表 (查询要删除的表)
async function analyzeActionLogTables (executeCleanup = false) {
  let client
  try {
    client = await connectDB(DB_CONFIGS.actionlog)
    const db = client.db(DB_CONFIGS.actionlog.dbName)

    // 获取所有 al_actionlog 开头的集合
    const collections = await db.listCollections({
      name: { $regex: /^al_actionlog/ }
    }).toArray()

    const threeMonthsAgoYM = getThreeMonthsAgoYearMonth()
    const threeMonthsAgoDate = getThreeMonthsAgo()

    log(LOG_LEVELS.INFO, `分析 al_actionlog 表，基准日期: ${threeMonthsAgoYM} (${threeMonthsAgoDate.toISOString()})`)

    const tablesToDelete = []
    let totalRecordsToDelete = 0

    for (const collectionInfo of collections) {
      const collectionName = collectionInfo.name

      // 提取日期部分 (假设格式为 al_actionlog_YYYYMM)
      const dateMatch = collectionName.match(/al_actionlog.*?(\d{6})$/)
      if (dateMatch) {
        const tableYearMonth = dateMatch[1]

        // 如果表的年月小于三个月前，则标记为待删除
        if (tableYearMonth < threeMonthsAgoYM) {
          try {
            // 获取表中的记录数
            const recordCount = await db.collection(collectionName).countDocuments()
            tablesToDelete.push({
              name: collectionName,
              yearMonth: tableYearMonth,
              recordCount: recordCount
            })
            totalRecordsToDelete += recordCount
          } catch (error) {
            log(LOG_LEVELS.WARN, `无法获取表 ${collectionName} 的记录数`, error)
            tablesToDelete.push({
              name: collectionName,
              yearMonth: tableYearMonth,
              recordCount: 'unknown'
            })
          }
        }
      }
    }

    // 显示分析结果
    log(LOG_LEVELS.INFO, `al_actionlog 分析结果:`)
    log(LOG_LEVELS.INFO, `  - 总表数: ${collections.length}`)
    log(LOG_LEVELS.INFO, `  - 待删除表数: ${tablesToDelete.length}`)
    log(LOG_LEVELS.INFO, `  - 预计删除记录数: ${totalRecordsToDelete}`)

    if (tablesToDelete.length > 0) {
      log(LOG_LEVELS.INFO, `  - 待删除的表:`)
      tablesToDelete.forEach(table => {
        log(LOG_LEVELS.INFO, `    * ${table.name} (${table.yearMonth}) - ${table.recordCount} 条记录`)
      })
    }

    // 如果需要执行清理
    if (executeCleanup && tablesToDelete.length > 0) {
      log(LOG_LEVELS.INFO, `开始执行 al_actionlog 表清理...`)
      let droppedCount = 0

      for (const table of tablesToDelete) {
        try {
          await db.collection(table.name).drop()
          droppedCount++
          log(LOG_LEVELS.INFO, `✓ 成功删除表: ${table.name}`)
        } catch (error) {
          log(LOG_LEVELS.ERROR, `✗ 删除表失败: ${table.name}`, error)
        }
      }

      log(LOG_LEVELS.INFO, `al_actionlog 清理完成，成功删除 ${droppedCount}/${tablesToDelete.length} 个表`)
    } else if (!executeCleanup && tablesToDelete.length > 0) {
      log(LOG_LEVELS.INFO, `使用 --run-now 参数执行实际清理操作`)
    }

    return {
      totalTables: collections.length,
      tablesToDelete: tablesToDelete.length,
      totalRecordsToDelete: totalRecordsToDelete,
      tableDetails: tablesToDelete
    }

  } catch (error) {
    log(LOG_LEVELS.ERROR, 'al_actionlog 分析过程中发生错误', error)
    return null
  } finally {
    if (client) {
      await client.close()
    }
  }
}

// 分析 wslog* 表 (查询要删除的表)
async function analyzeWsLogTables (executeCleanup = false) {
  let client
  try {
    client = await connectDB(DB_CONFIGS.wslog)
    const db = client.db(DB_CONFIGS.wslog.dbName)

    // 获取所有 wslog 开头的集合
    const collections = await db.listCollections({
      name: { $regex: /^wslog/ }
    }).toArray()

    const threeMonthsAgoYM = getThreeMonthsAgoYearMonth()
    const threeMonthsAgoDate = getThreeMonthsAgo()

    log(LOG_LEVELS.INFO, `分析 wslog 表，基准日期: ${threeMonthsAgoYM} (${threeMonthsAgoDate.toISOString()})`)

    const tablesToDelete = []
    let totalRecordsToDelete = 0

    for (const collectionInfo of collections) {
      const collectionName = collectionInfo.name

      // 提取日期部分 (假设格式为 wslog_YYYYMM 或 wslog_YYYY_MM)
      const dateMatch = collectionName.match(/wslog.*?(\d{6})$/) ||
        collectionName.match(/wslog.*?(\d{4})_?(\d{2})$/)

      if (dateMatch) {
        let tableYearMonth
        if (dateMatch[2]) {
          // wslog_YYYY_MM 格式
          tableYearMonth = dateMatch[1] + dateMatch[2]
        } else {
          // wslog_YYYYMM 格式
          tableYearMonth = dateMatch[1]
        }

        // 如果表的年月小于三个月前，则标记为待删除
        if (tableYearMonth < threeMonthsAgoYM) {
          try {
            // 获取表中的记录数
            const recordCount = await db.collection(collectionName).countDocuments()
            tablesToDelete.push({
              name: collectionName,
              yearMonth: tableYearMonth,
              recordCount: recordCount
            })
            totalRecordsToDelete += recordCount
          } catch (error) {
            log(LOG_LEVELS.WARN, `无法获取表 ${collectionName} 的记录数`, error)
            tablesToDelete.push({
              name: collectionName,
              yearMonth: tableYearMonth,
              recordCount: 'unknown'
            })
          }
        }
      }
    }

    // 显示分析结果
    log(LOG_LEVELS.INFO, `wslog 分析结果:`)
    log(LOG_LEVELS.INFO, `  - 总表数: ${collections.length}`)
    log(LOG_LEVELS.INFO, `  - 待删除表数: ${tablesToDelete.length}`)
    log(LOG_LEVELS.INFO, `  - 预计删除记录数: ${totalRecordsToDelete}`)

    if (tablesToDelete.length > 0) {
      log(LOG_LEVELS.INFO, `  - 待删除的表:`)
      tablesToDelete.forEach(table => {
        log(LOG_LEVELS.INFO, `    * ${table.name} (${table.yearMonth}) - ${table.recordCount} 条记录`)
      })
    }

    // 如果需要执行清理
    if (executeCleanup && tablesToDelete.length > 0) {
      log(LOG_LEVELS.INFO, `开始执行 wslog 表清理...`)
      let droppedCount = 0

      for (const table of tablesToDelete) {
        try {
          await db.collection(table.name).drop()
          droppedCount++
          log(LOG_LEVELS.INFO, `✓ 成功删除表: ${table.name}`)
        } catch (error) {
          log(LOG_LEVELS.ERROR, `✗ 删除表失败: ${table.name}`, error)
        }
      }

      log(LOG_LEVELS.INFO, `wslog 清理完成，成功删除 ${droppedCount}/${tablesToDelete.length} 个表`)
    } else if (!executeCleanup && tablesToDelete.length > 0) {
      log(LOG_LEVELS.INFO, `使用 --run-now 参数执行实际清理操作`)
    }

    return {
      totalTables: collections.length,
      tablesToDelete: tablesToDelete.length,
      totalRecordsToDelete: totalRecordsToDelete,
      tableDetails: tablesToDelete
    }

  } catch (error) {
    log(LOG_LEVELS.ERROR, 'wslog 分析过程中发生错误', error)
    return null
  } finally {
    if (client) {
      await client.close()
    }
  }
}

// 分析 wf_instance 表中的数据 (查询要删除的记录)
async function analyzeWorkflowInstanceData (executeCleanup = false) {
  let client
  try {
    client = await connectDB(DB_CONFIGS.workflow)
    const db = client.db(DB_CONFIGS.workflow.dbName)

    const threeMonthsAgo = getThreeMonthsAgo()
    log(LOG_LEVELS.INFO, `分析 wf_instance 数据，基准日期: ${threeMonthsAgo.toISOString()}`)

    // 查询总记录数
    const totalRecords = await db.collection('wf_instance').countDocuments()

    // 查询要删除的记录数 (completeDate 在三个月前的记录)
    const recordsToDelete = await db.collection('wf_instance').countDocuments({
      completeDate: { $lt: threeMonthsAgo }
    })

    // 获取一些样本数据用于展示
    const sampleRecords = await db.collection('wf_instance').find({
      completeDate: { $lt: threeMonthsAgo }
    }).limit(5).project({
      _id: 1,
      completeDate: 1,
      workflowId: 1,
      status: 1
    }).toArray()

    // 显示分析结果
    log(LOG_LEVELS.INFO, `wf_instance 分析结果:`)
    log(LOG_LEVELS.INFO, `  - 总记录数: ${totalRecords}`)
    log(LOG_LEVELS.INFO, `  - 待删除记录数: ${recordsToDelete}`)
    log(LOG_LEVELS.INFO, `  - 保留记录数: ${totalRecords - recordsToDelete}`)

    if (recordsToDelete > 0 && sampleRecords.length > 0) {
      log(LOG_LEVELS.INFO, `  - 待删除记录样本 (前5条):`)
      sampleRecords.forEach((record, index) => {
        const completeDate = record.completeDate ? record.completeDate.toISOString() : 'null'
        log(LOG_LEVELS.INFO, `    ${index + 1}. ID: ${record._id}, 完成时间: ${completeDate}, 工作流: ${record.workflowId || 'unknown'}, 状态: ${record.status || 'unknown'}`)
      })
    }

    // 如果需要执行清理
    if (executeCleanup && recordsToDelete > 0) {
      log(LOG_LEVELS.INFO, `开始执行 wf_instance 数据清理...`)

      // 删除 completeDate 在三个月前的记录
      const result = await db.collection('wf_instance').deleteMany({
        completeDate: { $lt: threeMonthsAgo }
      })

      log(LOG_LEVELS.INFO, `✓ wf_instance 清理完成，成功删除 ${result.deletedCount} 条记录`)
    } else if (!executeCleanup && recordsToDelete > 0) {
      log(LOG_LEVELS.INFO, `使用 --run-now 参数执行实际清理操作`)
    }

    return {
      totalRecords: totalRecords,
      recordsToDelete: recordsToDelete,
      recordsToKeep: totalRecords - recordsToDelete,
      sampleRecords: sampleRecords
    }

  } catch (error) {
    log(LOG_LEVELS.ERROR, 'wf_instance 分析过程中发生错误', error)
    return null
  } finally {
    if (client) {
      await client.close()
    }
  }
}

// 执行所有分析/清理任务
async function runCleanupTasks (executeCleanup = false) {
  const taskType = executeCleanup ? '清理' : '分析'
  log(LOG_LEVELS.INFO, `=== 开始执行MongoDB日志${taskType}任务 ===`)

  const startTime = Date.now()

  try {
    // 并行执行分析/清理任务
    const results = await Promise.all([
      analyzeActionLogTables(executeCleanup),
      analyzeWsLogTables(executeCleanup),
      analyzeWorkflowInstanceData(executeCleanup)
    ])

    const endTime = Date.now()
    const duration = Math.round((endTime - startTime) / 1000)

    // 汇总统计信息
    const [actionLogResult, wsLogResult, workflowResult] = results

    log(LOG_LEVELS.INFO, `=== ${taskType}任务汇总统计 ===`)

    if (actionLogResult) {
      log(LOG_LEVELS.INFO, `al_actionlog: ${actionLogResult.tablesToDelete}/${actionLogResult.totalTables} 个表, ${actionLogResult.totalRecordsToDelete} 条记录`)
    }

    if (wsLogResult) {
      log(LOG_LEVELS.INFO, `wslog: ${wsLogResult.tablesToDelete}/${wsLogResult.totalTables} 个表, ${wsLogResult.totalRecordsToDelete} 条记录`)
    }

    if (workflowResult) {
      log(LOG_LEVELS.INFO, `wf_instance: ${workflowResult.recordsToDelete}/${workflowResult.totalRecords} 条记录`)
    }

    const totalTablesToDelete = (actionLogResult?.tablesToDelete || 0) + (wsLogResult?.tablesToDelete || 0)
    const totalRecordsToDelete = (actionLogResult?.totalRecordsToDelete || 0) +
      (wsLogResult?.totalRecordsToDelete || 0) +
      (workflowResult?.recordsToDelete || 0)

    log(LOG_LEVELS.INFO, `总计: ${totalTablesToDelete} 个表, ${totalRecordsToDelete} 条记录`)
    log(LOG_LEVELS.INFO, `=== 所有${taskType}任务完成，耗时: ${duration}秒 ===`)

    if (!executeCleanup && (totalTablesToDelete > 0 || totalRecordsToDelete > 0)) {
      log(LOG_LEVELS.WARN, `⚠️  使用 --run-now 参数执行实际清理操作`)
    }

    return {
      actionLog: actionLogResult,
      wsLog: wsLogResult,
      workflow: workflowResult,
      summary: {
        totalTablesToDelete,
        totalRecordsToDelete,
        duration
      }
    }

  } catch (error) {
    log(LOG_LEVELS.ERROR, `${taskType}任务执行失败`, error)
    return null
  }
}

// 主函数
async function main () {
  log(LOG_LEVELS.INFO, 'MongoDB日志清理脚本启动')

  const executeCleanup = process.argv.includes('--run-now')

  if (executeCleanup) {
    // 立即执行一次清理
    log(LOG_LEVELS.INFO, '检测到 --run-now 参数，将执行实际清理操作')
    await runCleanupTasks(true)
    process.exit(0)
  } else {
    // 只执行分析，不清理
    log(LOG_LEVELS.INFO, '执行分析模式，不会删除任何数据')
    await runCleanupTasks(false)

    // 保持进程运行
    process.exit(0)
  }
}

// 启动脚本
if (require.main === module) {
  main().catch(error => {
    log(LOG_LEVELS.ERROR, '脚本启动失败', error)
    process.exit(1)
  })
}

module.exports = {
  runCleanupTasks,
  analyzeActionLogTables,
  analyzeWsLogTables,
  analyzeWorkflowInstanceData
}