const { createDefaultPermissions } = require('accel-utils')

// app
const apps = []
// 基础视图配置
const pageGroups = [
  {
    sId: 'mingdaoGroup',
    name: '明道云管理',
    pages: [
      {
        sId: 'MingdaoOrderManagement', name: '支付管理1.0.6', icon: 'paid',
        meta: {
          modelId: 'mingdao_order',
          modelPath: 'mingdaoOrders',
        }
      },
      {
        sId: 'MingdaoRefundManagement', name: '退款管理1.0.6', icon: 'money_off_csred',
        meta: {
          modelId: 'mingdao_refund',
          modelPath: 'mingdaoRefunds',
        }
      },
      {
        sId: 'MingdaoConfigManagement', name: '应用管理1.0.6', icon: 'account_box',
        meta: {
          modelId: 'mingdao_config',
          modelPath: 'mingdaoConfigs',
        }
      },
      {
        sId: 'MingdaoMenuConfigManagement', name: '应用菜单管理', icon: 'account_box',
        meta: {
          modelId: 'mingdao_menuconfig',
          modelPath: 'mingdaoMenuConfigs',
        }
      },
      {
        sId: 'MingdaoUserManagement', name: '用户管理1.0.6', icon: 'account_box',
        meta: {
          modelId: 'mingdao_user',
          modelPath: 'mingdaoUsers',
        }
      },
      {
        sId: 'MingdaoUserTypeManagement', name: '用户类型管理', icon: 'account_box',
        meta: {
          modelId: 'mingdao_usertype',
          modelPath: 'mingdaoUserTypes',
        }
      },
      {
        sId: 'MingdaoVipOrderManagement', name: 'vip订单管理', icon: 'account_box',
        meta: {
          modelId: 'vip_order',
          modelPath: 'vipOrders',
        }
      },
    ],
  },
]
// 基础功能配置
const functions = [
  {
    name: '明道云管理',
    sId: 'MingdaoManagementFunction',
    pages: [
      'MingdaoOrderManagement',
      'MingdaoRefundManagement',
      'MingdaoConfigManagement',
      'MingdaoUserManagement',
      'MingdaoUserTypeManagement',
      'MingdaoShareOrderManagement',
      'MingdaoVipOrderManagement',
      'MingdaoMenuConfigManagement',

    ],
    apiPermissions: [
      ...createDefaultPermissions({
        type: 'application',
        controller: 'mingdao_config',
      }),
      ...createDefaultPermissions({
        type: 'application',
        controller: 'mingdao_order',
      }),
      ...createDefaultPermissions({
        type: 'application',
        controller: 'mingdao_refund',
      }),
      ...createDefaultPermissions({
        type: 'application',
        controller: 'mingdao_user',
        mode: 'branch'
      }),
      ...createDefaultPermissions({
        type: 'application',
        controller: 'mingdao_usertype',
        mode: 'branch'
      }),
      ...createDefaultPermissions({
        type: 'application',
        controller: 'mingdao_clerk',
        mode: 'branch'
      }),
      ...createDefaultPermissions({
        type: 'application',
        controller: 'mingdao_shareOrder',
        mode: 'branch'
      }),
      ...createDefaultPermissions({
        type: 'application',
        controller: 'vip_order',
        mode: 'branch'
      }),
      ...createDefaultPermissions({
        type: 'application',
        controller: 'mingdao_menuconfig',
        mode: 'branch'
      }),
    ]
  },
  {
    name: '已登录',
    sId: 'AuthenticatedFunction',
    pages: [],
    apiPermissions: []
  },
  {
    name: '明道云客户权限',
    sId: 'MingdaoUserPermissionFunction',
    apiPermissions: [
      { 'type': 'application', 'controller': 'order', 'action': 'getDetail' },
      { 'type': 'application', 'controller': 'order', 'action': 'getDetailById' },
      { 'type': 'application', 'controller': 'order', 'action': 'getOrderList' },
      { 'type': 'application', 'controller': 'order', 'action': 'getACodeUrl' },
      { 'type': 'application', 'controller': 'pay', 'action': 'createPayOrder' },
      { 'type': 'application', 'controller': 'pay', 'action': 'getH5PayParams' },
      { 'type': 'application', 'controller': 'pay', 'action': 'refund' },

      { 'type': 'application', 'controller': 'customer', 'action': 'syncUser' },
      { 'type': 'application', 'controller': 'pay', 'action': 'wxPay' },

      { 'type': 'application', 'controller': 'mingdao_shareOrder', 'action': 'setShareOrderList' },
      { 'type': 'application', 'controller': 'mingdao_shareOrder', 'action': 'getShareOrderList' },
    ]
  },
  {
    name: '未登录',
    sId: 'PublicFunction',
    apiPermissions: [
      // 微信接收支付成功回调
      { 'type': 'application', 'controller': 'wechat_pay', 'action': 'callbackPayOrder' },
      { 'type': 'application', 'controller': 'wechat_pay', 'action': 'notifyRefund' },
      { 'type': 'application', 'controller': 'wechat_pay', 'action': 'callbackPayVip' },

      // 通联支付回调
      { 'type': 'application', 'controller': 'tonglian_pay', 'action': 'callbackPayOrder' },
      // 商品列表和规格列表
      { 'type': 'application', 'controller': 'goods', 'action': 'getGoodList' },
      { 'type': 'application', 'controller': 'goods', 'action': 'getSkus' },
      { 'type': 'application', 'controller': 'wechat', 'action': 'wechatNotice' },
      { 'type': 'application', 'controller': 'order', 'action': 'getDetail' },
      { 'type': 'application', 'controller': 'order', 'action': 'getDetailById' },
      { 'type': 'application', 'controller': 'pay', 'action': 'createPayOrder' },
      { 'type': 'application', 'controller': 'pay', 'action': 'getH5PayParams' },
      { 'type': 'application', 'controller': 'uploads', 'action': 'uploadImages' },

      { 'type': 'application', 'controller': 'mingdao_shareOrder', 'action': 'setShareOrderList' },
      { 'type': 'application', 'controller': 'mingdao_shareOrder', 'action': 'getShareOrderList' },
      { 'type': 'application', 'controller': 'mingdao_user', 'action': 'addClerkUser' },
      { 'type': 'application', 'controller': 'mingdao_user', 'action': 'getClerkList' },
      { 'type': 'application', 'controller': 'pay', 'action': 'wxPay' },
      { 'type': 'application', 'controller': 'mingdao_menuconfig', 'action': 'openGetAppConfig' },
    ]
  }
]

const roles = [
  {
    name: '管理员',
    type: 'admin',
    description: '平台管理员',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'CoreContentPermissionFunction',
      'MingdaoManagementFunction',
      'MingdaoUserPermissionFunction',
    ]
  },
  {
    name: '明道云客户',
    type: 'mingdaoCustomer',
    description: '明道云客户',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'MingdaoManagementFunction',
      'MingdaoUserPermissionFunction',
    ]
  }
]

module.exports = {
  pageGroups,
  functions,
  roles,
  apps
}
