'use strict';

module.exports = {
  "collectionName": "mingdao_menuconfig",
  "info": {
    "name": "mingdao_menuconfig",
    "label": "明道云菜单链接管理",
    "description": "明道云菜单链接管理"
  },
  "options": {
    "draftAndPublish": false,
    "timestamps": true,
    "allOf": [
      {
        "if": {
          "properties": {
            "type": {
              "const": "store"
            }
          }
        },
        "then": {
          "properties": {
            "associateClerk": { "visible": true },
            "clerkNumber": { "visible": true }
          }
        }
      }
    ]
  },
  "pluginOptions": {

  },
  "attributes": {
    mingdao_configs: {
      label: '应用配置',
      model: 'mingdao_config',
      visible: true,
      required: true,
      unique: true,
    },
    forewarningUrl: {
      label: '预警URL',
      type: 'string',
      visible: true,
    },
    forewarningNumber: {
      label: '预警数量',
      type: 'number',
      visible: true,
    },
    "pBranch": {
      "label": "租户",
      "plugin": "users-permissions",
      "model": "branch",
      "configurable": false
    },
  }
}
