'use strict';

/**
 * An asynchronous bootstrap function that runs before
 * your application gets started.
 *
 * This gives you an opportunity to set up your data model,
 * run jobs, or perform some special logic.
 *
 * See more details here: https://strapi.io/documentation/developer-docs/latest/setup-deployment-guides/configurations.html#bootstrap
 */

async function updateDefaultApp() {
  console.log('Users-Permissions update Default App...')
  console.time('Users-Permissions update Default App')
  const appQuery = strapi.query('app', 'users-permissions')
  // 添加公众号、小程序默认配置
  const miniApp = await appQuery.findOne({ id: '658d902af05bab3d147294f3' })
  if (!miniApp) {
    await appQuery.create({
      id: '658d902af05bab3d147294f3',
      type: 'wechatMiniProgram',
      name: '美美缝美缝超市',
      tokenSecret: 'qhgRzDfVgFyjWnRBbwmu1QZ9',
      verifyData: {
        appid: "wxe90e67a76348d51b",
        secret: "cdff0aa55798179057d1a963b6d78ed0"
      }
    })
  }
  const officialApp = await appQuery.findOne({ id: '658d902af05bab3d147294f4' })
  if (!officialApp) {
    await appQuery.create({
      id: '658d902af05bab3d147294f4',
      type: 'wechatOfficialAccount',
      name: '美美缝美缝专家',
      tokenSecret: 'ckHbKpRr0ZkjozdDgCpJPiYT',
      verifyData: {
        appid: "wx09dc933308f20830",
        secret: "d4cef0e64fe2ce4f2528f2adfdf1ef59"
      },
    })
  }
  const official1App = await appQuery.findOne({ id: '658d902af05bab3d147294f5' })
  if (!official1App) {
    await appQuery.create({
      id: '658d902af05bab3d147294f5',
      type: 'wechatOfficialAccount',
      name: '美美缝美缝超级集市',
      tokenSecret: 'nllrcCkk3rA5sir0YF4yvXWs',
      verifyData: {
        appid: "wxba80bf62facbdba4",
        secret: "895115bb6b8ddd31a50b38f7468c601a"
      },
    })
  }

  const t0mmdApp = await appQuery.findOne({ id: '658d902af05bab3d147294f6' })
  if (!t0mmdApp) {
    await appQuery.create({
      id: '658d902af05bab3d147294f6',
      type: 'wechatOfficialAccount',
      name: '美美哒批零专家',
      tokenSecret: 'kS0b4rD6cFwXqQnJtTq5a9eZ',
      verifyData: {
        appid: "wx7090745609fd7d58",
        secret: "6d6bf1347215d810c5d998f4355cd331"
      },
    })
  }
  console.timeEnd('Users-Permissions update Default App')
}

async function updateDefaultBranch() {
  console.log('Users-Permissions update Branch App...')
  console.time('Users-Permissions update Branch App')
  const branchQuery = strapi.query('branch', 'users-permissions')
  // 添加美美哒、米多多 默认租户配置
  const mmdBranch = await branchQuery.findOne({ id: '663dd4a8aa6fc0aabbd3168a' })
  if (!mmdBranch) {
    await branchQuery.create({
      id: '663dd4a8aa6fc0aabbd3168a',
      type: 'mmd',
      domain: 'mmd',
      name: '美美哒',
    })
  }
  const mddBranch = await branchQuery.findOne({ id: '6641ed04f4165e2de5f20b30' })
  if (!mddBranch) {
    await branchQuery.create({
      id: '6641ed04f4165e2de5f20b30',
      type: 'mdd',
      domain: 'mdd',
      name: '米多多',
    })
  }
  console.timeEnd('Users-Permissions update Default App')
}

async function updateDefaultMingdaoConfig() {
  console.log('mingdao config update Default...')
  console.time('mingdao config update Default')
  const configQuery = strapi.query('mingdao_config')
  // 添加美美哒、米多多 明道云默认配置
  const mmdConfig = await configQuery.findOne({ id: '663f0d852a31b6deeacb2c6e' })
  if (!mmdConfig) {
    await configQuery.create({
      id: '663f0d852a31b6deeacb2c6e',
      name: '美美哒',
      app_id: '017e9387-a802-41cd-a500-970a4789d26b',
      app_key: 'f52a3866a2b484bb',
      sign: 'ZWJlODQxY2M5OGQ2YmY5NTU0ZGUyZTIzN2Q5Mjc0OTRkMGY1YzhmMGFiODhiYjkzMGI5OTE3MzM2OGQyNTZiNA==',
      base_url: 'http://************:8880/api',
      pay_success_notice_url: 'http://************:8880/api/workflow/hooks/NjU4ZDM0YjI2YzdhY2E2YmRiNmNiNGNi',
      refund_success_notice_url: 'http://************:8880/api/workflow/hooks/NjU4ZDM0YjI2YzdhY2E2YmRiNmNiNGM4',
      refund_fail_notice_url: 'http://************:8880/api/workflow/hooks/NjU4ZDM0YjI2YzdhY2E2YmRiNmNiNGNj',
      tong_lian: {
        cus_id: '5634910503950CR',
        app_id: '00282821',
        private_key: 'MIIEpAIBAAKCAQEAxaqst92Xaz8KZ2u2KXg3UeKE0/40MPPLYRFGWIxjPyzYYXlIMCpjEgOFjmBLAb3hkhRwjTBjAop+9l8DaCasdx8XddujRx+mGr6BYCaChEYKZ2tImljtj3s8EQxReC9PPBrSGN28VuGEnE6FbuBvgI8l6kBM7weUoc7twwEXz7pNvmcAfFX+O+rFEWhAwQw+6A0ka5yUQOQ8OGvjZMGMdyWpTvUuCUo8Fw4LvaGYNWLV0DtLfiE/GGCstFMfrQ6M+/V63W3MATthNUWodZo7iUNXZuqXN6ci6p36Ot2t72xS8DSu4JaKXi5lFWttcW0HIrhlRtXv2Eu0CiHP/WDQXQIDAQABAoIBAA9EfY6+GOH5vo36bdmKJYu3mVbefZ2CVyUqNmrEfS32AsiUm5TM0VYk234BxgleZzvnREJDiII7vE6/S/4RbqTMy/0Yd+YYdbX/HPMWRmJ9HnSafsMdN0wr0pnmf6xuywnzcGLnTZLQXHPORNQplbMyW1VanhagWUSePRMg1HwA3wW/L3FVtofAzKgxHPGccf/hu43h/MLTT7nNJZOlF0Tnn1/59LwAzgG8UiYHRBBwfPHsRMpED4LsWXucg6Xms4gYrxpK5K0HPK/TNbYy48JBh66jK6vpVpLWp5MqWuZoMXS3xETdQtF4nBiOlHjsrv+CEQlldqBZ2kEf6qAu9UECgYEA8Y0EPkFNPiBUImf0RG8/0iKZXsau79CYnPwYILF69X1s5/29TVGvSF499Xlq1kOT5ZkcR4Gh0F4Ufjn8mh3SbwPg6RTsoRvgeqEoQaMSShfeo299LpA0TlDyv7RPJlAcpIL7iYudpAb8/1rae8tt/VGyT2xrhAC6tdP40MPQmsUCgYEA0X2jolfnRyoWDvBeLo1ngEahA0Lw8pVPo9UDYY9vtblZDpzovu/DtppAD6ygP3s80FVnTHsW2+pZT1ose4Zq4AcJ/VpbZCX93GkILzjcMyE4gWKZSltV+rkqC2aZJZLQpjARwA2WTHX4X3/39Z5ALh4+zPRIoy2eApzQgcyMmLkCgYBmU5nPkbRaNDHQHX+iGgrMRzH3r21eVFzCbB0AhI8VPhwLrzcpcQgzPRE8U0JKIHif1Bvhwu28qo495uy2kRmbQ+ZcdyvDGF8hM/Is1BskXjZHwCwktJwj7TDYu1aoYVmw63gJjaarXPaCaoeR19zxalGCSejGE9UEwBZwwfM2DQKBgQCEwhY8/2/1zVoFRZap8bqxtBC9vGJtmOnLbWajIP8uLrOdaxqkRdXFlqMaS8+R60tyy2yXLzohS7Ylb4PZFCoOpudRwz0/jkP+tiEVq/cAAAGLHSz/zjoCle1rJC3RfeT0LGpTR0kEU45/Ft3QreUGVOx/fh+6KWKFTVNlrnc7IQKBgQCF3MbhEyvflj05ybTQt5+GhRxehDRE3/jNwNnWweRkLya5f/08Om76uMw9RqwhQv59SMsdqCLxnw6/wHfNEw8sFr8iWTsLstL7N3so7hZBGEzcOxPD09NAU/+QKK3NI8KUXn4BqeMoG2jlCjsOev3LRTd/gEPFe81OhbVXFYxMdg=='
      },
      pBranch: '663dd4a8aa6fc0aabbd3168a'
    })
  }
  const mddConfig = await configQuery.findOne({ id: '663f0f3e11df5d3ecb6e378b' })
  if (!mddConfig) {
    await configQuery.create({
      id: '663f0f3e11df5d3ecb6e378b',
      name: '米多多',
      app_id: '509ed538-9088-461a-a186-a3d9d44efb83',
      app_key: 'f37f8e1ab20bf6b3',
      sign: 'M2U3MDg0ZjM0MWU4YjAyNzE0MGI2MGRhZjU2Mzg3ZWI3MzNkZmI5MDBiMmRlYjE5NTE5YjViMmIyNzZlM2U2YQ==',
      base_url: 'http://************:8880/api',
      pay_success_notice_url: 'http://************:8880/api/workflow/hooks/NjYzZDk5ZTdlYTk1ODk2MzVkYjM2YzFj',
      refund_success_notice_url: 'http://************:8880/api/workflow/hooks/NjYzZDk5ZTdlYTk1ODk2MzVkYjM2YzFi',
      refund_fail_notice_url: 'http://************:8880/api/workflow/hooks/NjYzZDk5ZTdlYTk1ODk2MzVkYjM2YzFk',
      tong_lian: {
        cus_id: '5634910503950CR',
        app_id: '00282821',
        private_key: 'MIIEpAIBAAKCAQEAxaqst92Xaz8KZ2u2KXg3UeKE0/40MPPLYRFGWIxjPyzYYXlIMCpjEgOFjmBLAb3hkhRwjTBjAop+9l8DaCasdx8XddujRx+mGr6BYCaChEYKZ2tImljtj3s8EQxReC9PPBrSGN28VuGEnE6FbuBvgI8l6kBM7weUoc7twwEXz7pNvmcAfFX+O+rFEWhAwQw+6A0ka5yUQOQ8OGvjZMGMdyWpTvUuCUo8Fw4LvaGYNWLV0DtLfiE/GGCstFMfrQ6M+/V63W3MATthNUWodZo7iUNXZuqXN6ci6p36Ot2t72xS8DSu4JaKXi5lFWttcW0HIrhlRtXv2Eu0CiHP/WDQXQIDAQABAoIBAA9EfY6+GOH5vo36bdmKJYu3mVbefZ2CVyUqNmrEfS32AsiUm5TM0VYk234BxgleZzvnREJDiII7vE6/S/4RbqTMy/0Yd+YYdbX/HPMWRmJ9HnSafsMdN0wr0pnmf6xuywnzcGLnTZLQXHPORNQplbMyW1VanhagWUSePRMg1HwA3wW/L3FVtofAzKgxHPGccf/hu43h/MLTT7nNJZOlF0Tnn1/59LwAzgG8UiYHRBBwfPHsRMpED4LsWXucg6Xms4gYrxpK5K0HPK/TNbYy48JBh66jK6vpVpLWp5MqWuZoMXS3xETdQtF4nBiOlHjsrv+CEQlldqBZ2kEf6qAu9UECgYEA8Y0EPkFNPiBUImf0RG8/0iKZXsau79CYnPwYILF69X1s5/29TVGvSF499Xlq1kOT5ZkcR4Gh0F4Ufjn8mh3SbwPg6RTsoRvgeqEoQaMSShfeo299LpA0TlDyv7RPJlAcpIL7iYudpAb8/1rae8tt/VGyT2xrhAC6tdP40MPQmsUCgYEA0X2jolfnRyoWDvBeLo1ngEahA0Lw8pVPo9UDYY9vtblZDpzovu/DtppAD6ygP3s80FVnTHsW2+pZT1ose4Zq4AcJ/VpbZCX93GkILzjcMyE4gWKZSltV+rkqC2aZJZLQpjARwA2WTHX4X3/39Z5ALh4+zPRIoy2eApzQgcyMmLkCgYBmU5nPkbRaNDHQHX+iGgrMRzH3r21eVFzCbB0AhI8VPhwLrzcpcQgzPRE8U0JKIHif1Bvhwu28qo495uy2kRmbQ+ZcdyvDGF8hM/Is1BskXjZHwCwktJwj7TDYu1aoYVmw63gJjaarXPaCaoeR19zxalGCSejGE9UEwBZwwfM2DQKBgQCEwhY8/2/1zVoFRZap8bqxtBC9vGJtmOnLbWajIP8uLrOdaxqkRdXFlqMaS8+R60tyy2yXLzohS7Ylb4PZFCoOpudRwz0/jkP+tiEVq/cAAAGLHSz/zjoCle1rJC3RfeT0LGpTR0kEU45/Ft3QreUGVOx/fh+6KWKFTVNlrnc7IQKBgQCF3MbhEyvflj05ybTQt5+GhRxehDRE3/jNwNnWweRkLya5f/08Om76uMw9RqwhQv59SMsdqCLxnw6/wHfNEw8sFr8iWTsLstL7N3so7hZBGEzcOxPD09NAU/+QKK3NI8KUXn4BqeMoG2jlCjsOev3LRTd/gEPFe81OhbVXFYxMdg=='
      },
      pBranch: '6641ed04f4165e2de5f20b30'
    })
  }
  console.timeEnd('mingdao config  update Default App')
}

module.exports = async () => {
  await updateDefaultApp()
  await updateDefaultBranch()
  await updateDefaultMingdaoConfig()
};
