const { BranchCurdRouter } = require('accel-utils')
const {includes} = require("lodash");

const branchCurdRouter = new (class extends BranchCurdRouter {

  constructor(modelName) {
    super(modelName)
  }

  async branchCreate(ctx) {
    const { account } = ctx.request.body
    if (includes(account, '@')) {
      ctx.request.body.accountType = 'email'
    } else {
      ctx.request.body.accountType = 'phone'
    }
    return super.branchCreate(ctx);
  }

  async branchUpdate(ctx) {
    const { account } = ctx.request.body
    if (includes(account, '@')) {
      ctx.request.body.accountType = 'email'
    } else {
      ctx.request.body.accountType = 'phone'
    }
    return super.branchUpdate(ctx);
  }


})('mingdao_usertype')


module.exports = {
  ...branchCurdRouter.createHandlers()
}
