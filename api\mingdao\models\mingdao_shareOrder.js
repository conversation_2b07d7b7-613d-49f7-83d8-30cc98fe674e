'use strict';

module.exports = {
  "collectionName": "mingdao_shareOrder",
  "info": {
    "name": "shareOrder",
    "label": "分享订单",
    "description": "分享订单"
  },
  "options": {
    "draftAndPublish": false,
    "timestamps": true
  },
  "pluginOptions": {},
  "attributes": {
    "clientName": {
      "label": "客户姓名",
      "type": "string",
    },
    "mobile": {
      "label": "手机号",
      "type": "string",
    },
    "oddNumber": {
      "label": "销售单号",
      "type": "string",
    },
    "orderDate": {
      "label": "销售日期",
      "type": "string",
    },
    "addressInfo": {
      "label": "地址信息",
      "type": "object",
    },
    "orderAmount": {
      "label": "订单金额",
      "type": "string",
    },
    "fullDeduction": {
      "label": "满减金额",
      "type": "string",
    },
    "discount": {
      "label": "整的折扣",
      "type": "number",
    },
    "discountPrice": {
      "label": "优惠金额",
      "type": "string",
    },
    "freightPrice": {
      "label": "运费-客户承担",
      "type": "string",
    },
    "shoppingCart": {
      "label": "购物车",
      "type": "object",
    },
    "deliveryDate": {
      "label": "发货日期",
      "type": "string",
    },
    "amountPayable": {
      "label": "应付金额",
      "type": "string"
     },
    "pBranch": {
      "label": "租户",
      "plugin": "users-permissions",
      "model": "branch",
      "configurable": false
    },
  }
}
