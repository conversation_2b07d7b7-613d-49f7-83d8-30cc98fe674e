const { createDefaultPermissions: CP } = require('accel-utils')

const pageGroups = [
  {
    sId: 'PlatformOps',
    name: '平台运维🦸‍♂️',
    pages: [
      {
        sId: 'SystemRequestLogManagement', name: '请求日志', icon: 'table_chart',
        meta: {
          modelId: 'system-request-log',
          modelPath: 'system-request-logs',
        }
      },
      {
        sId: 'SystemErrorLogManagement', name: '报错日志', icon: 'bug_report',
        meta: {
          modelId: 'system-error-log',
          modelPath: 'system-error-logs',
        }
      },
    ],
  }
]
const functions = [
  {
    name: '日志管理',
    sId: 'LogManagementFunction',
    pages: [
      'SystemRequestLogManagement',
      'SystemErrorLogManagement',
    ],
    apiPermissions: [
      ...CP({ type: 'log', controller: 'system-request-log' }),
      ...CP({ type: 'log', controller: 'system-error-log' }),
    ],
  },
]

module.exports = {
  usersPermissionsConfig: {
    pageGroups,
    functions,
  }
}
