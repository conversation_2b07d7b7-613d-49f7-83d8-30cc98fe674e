const crypto = require('crypto')

async function wechatNotice(ctx) {
  let { signature, timestamp, nonce, echostr } = ctx.request.query;

  console.log(ctx.request.query);
  if (!signature || !timestamp || !nonce || !echostr) {
    console.log('params err');
    return ctx.response.send('false');
  }

  let signParams = [timestamp, nonce, 'KzEBiRwGxnAGzwZ5Er4OEowg'].sort();
  let signStr = signParams.join('');

  let signRes = crypto.createHash('sha1').update(signStr).digest('hex');

  console.log('My result: ' + signRes);
  if (signRes === signature) {
    console.log('check success');
    ctx.response.set('Content-Type', 'text/plain');
    return ctx.response.send(echostr);
  } else {
    console.log('check success');
    return ctx.response.end();
  }
}

module.exports = {
  wechatNotice
}
