{"name": "template-serv", "private": true, "version": "0.1.0", "description": "Server Template", "scripts": {"preinstall": "git submodule update --init --recursive --force", "debug-dev": "node ./node_modules/strapi/bin/strapi.js dev", "develop": "strapi develop --watch-admin", "develop-local-dev": "cross-env DATABASE=local SERVER=test strapi develop --watch-admin", "develop-local-dev-sync-callback": "cross-env DATABASE=local CALLBACK=true SERVER=test strapi develop --watch-admin", "develop-test-dev": "cross-env DATABASE=test SERVER=test strapi develop", "develop-prod-dev": "cross-env DATABASE=prodDev SERVER=test strapi develop --watch-admin", "develop-local-start": "cross-env DATABASE=local SERVER=local MINGDAO=local node bin/www", "develop-test-start": "cross-env DATABASE=test SERVER=test node bin/www", "web": "cross-env strapi watch-admin -- --browser true", "web-local": "cross-env WEB_ENV=local strapi watch-admin -- --browser true", "start": "strapi start", "start-test": "cross-env DATABASE=test SERVER=test strapi start", "build": "strapi build", "build-test": "cross-env WEB_ENV=test strapi build", "strapi configuration:dump": "strapi config:dump -p -f config.dump.json", "strapi configuration:restore": "strapi config:restore -f config.dump.json"}, "workspaces": ["packages/strapi", "packages/strapi-admin", "packages/accel-utils", "plugins/*"], "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^8.5.6", "cross-env": "^7.0.3", "exceljs": "^4.3.0", "jest": "^29.5.0"}, "dependencies": {"@buffetjs/core": "3.3.8", "@buffetjs/custom": "3.3.8", "@buffetjs/hooks": "3.3.8", "@buffetjs/icons": "3.3.8", "@buffetjs/styles": "3.3.8", "@buffetjs/utils": "3.3.8", "@purest/providers": "^1.0.2", "axios": "^1.4.0", "bcryptjs": "^2.4.3", "grant-koa": "5.4.8", "http-proxy-middleware": "^3.0.0", "immutable": "^3.8.2", "jsonwebtoken": "^8.5.1", "knex": "0.21.18", "koa-body": "^4.2.0", "koa2-connect": "^1.0.2", "koa2-ratelimit": "^0.9.0", "lodash": "^4.17.21", "mongodb": "^4.2.0", "mysql": "^2.18.1", "purest": "3.1.0", "request": "^2.83.0", "superagent": "^7.1.1", "tencentcloud-sdk-nodejs": "^4.0.276", "ua-parser-js": "^1.0.2", "uuid": "^3.1.0", "wechatpay-node-v3": "^2.1.8", "xml2js": "^0.4.23"}, "author": {"name": "A Strapi developer"}, "strapi": {"uuid": "56740e37-4cf9-48cc-98a4-494e770fd9a0"}, "engines": {"node": ">=10.16.0", "npm": "^6.0.0"}, "license": "MIT"}