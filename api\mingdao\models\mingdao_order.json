{"collectionName": "mingdao_order", "info": {"name": "MingdaoOrder", "label": "明道云支付记录", "description": "明道云支付记录信息"}, "options": {"draftAndPublish": false, "timestamps": true}, "pluginOptions": {}, "attributes": {"order_no": {"label": "订单编号", "type": "string", "unique": true, "editable": false}, "mingdao_order_no": {"label": "明道云订单编号", "type": "string", "editable": false}, "pay_status": {"label": "订单状态", "type": "number", "options": [{"value": 0, "label": "未支付"}, {"value": 1, "label": "已支付"}, {"value": 2, "label": "已退款"}, {"value": 3, "label": "已关闭"}], "editable": false}, "order_price": {"label": "订单价格", "type": "integer", "editable": false}, "payment_channel": {"label": "支付渠道", "type": "number", "options": [{"value": 0, "label": "微信小程序"}, {"value": 1, "label": "微信公众号"}, {"value": 2, "label": "H5"}]}, "payment_type": {"label": "支付方式", "type": "number", "options": [{"value": 0, "label": "微信支付"}, {"value": 1, "label": "支付宝支付"}, {"value": 2, "label": "通联支付"}], "editable": false}, "payment_merchant": {"label": "微信商户", "type": "string", "editable": false}, "transaction_id": {"label": "交易单号", "type": "string", "editable": false}, "payment_time": {"label": "支付时间", "type": "datetime", "editable": false}, "payment_user_openid": {"label": "支付人", "type": "string", "editable": false}, "payInfo": {"label": "支付信息", "type": "json"}, "refundInfo": {"label": "退款信息", "collection": "mingdao_refund", "via": "order", "editable": false}, "pBranch": {"label": "租户", "plugin": "users-permissions", "model": "branch", "configurable": false}}}