const { getAllList, getList } = require('../utils/mingdaoApi')
const worksheet = strapi.config.mingdao.worksheet
const mingdaoApi = require('../utils/mingdaoApi')

async function syncUser(ctx) {
  let { phone, configId } = ctx.request.body
  
  // 多租户支付为不影响线上美美哒使用 兼容处理 
  // TODO 后续删除
  if (!configId || configId === 'undefined') {
    configId = '663f0d852a31b6deeacb2c6e'
  }
  
  console.log(`-----${phone}-----`)
  const filter = []
  if (phone && phone.length >= 11) {
    filter.push({
        'controlId': 'phone',
        'dataType': 2,
        'spliceType': 1,
        'filterType': 2,
        'value': phone
      })
  }

  const list = await getAllList(worksheet.customer, configId, filter) || []
  console.log('---', list.length)
  const mingdaoCustomerRole = await strapi.query('role', 'users-permissions').findOne({ type: 'mingdaoCustomer' })

  let index = 0
  let func = async function (item) {
    await syncOne(ctx, item, configId, mingdaoCustomerRole.id)
    index++
    console.log(`同步进度：${item.name}-${item.phone}-${index}/${list.length}`)
  }

  await Promise.all(list.map((item) => func(item)))

  // 获取用户表对象
  // 写入用户表
  return ctx.wrapper.succ(`同步完成，用户数量：${list.length}`)
}

async function syncOne(ctx, mingdaoUser, configId, roleId) {
  const iPlugin = strapi.plugins['users-permissions']
  if (mingdaoUser.phone && mingdaoUser.phone.startsWith('+86')) {
    mingdaoUser.phone = mingdaoUser.phone.substring(3, mingdaoUser.phone.length)
  }
  const mingdaoConfig = await mingdaoApi.getMingdaoConfig(configId);
  const branch = mingdaoConfig.pBranch;
  let user = await iPlugin.services['user'].findOne({ phone: mingdaoUser.phone })
  if (!user) {
    // 用户不存在，先创建一个
    await iPlugin.services['user'].createNewUser(ctx, {
      username: mingdaoUser.name,
      phone: mingdaoUser.phone,
      pBranch: branch.id,
      customId: mingdaoUser.rowid,
      provider: 'mingdao',
      role: roleId
    }, null)
    console.log(`用户同步完成`)
  } else {
    console.log(`用户已存在`)
    if (mingdaoUser.name !== user.username) {
      // 名字变更，更新数据, 更新自定义id 。
      console.log(`名字变更，更新数据`)

     let newUser =  await iPlugin.services['user'].update({
        id: user.id
      }, {
        username: mingdaoUser.name,
        pBranch: branch.id,
        customId: mingdaoUser.rowid,
        provider: 'mingdao',
        role: roleId
      })
      // 更新租户、三方数据
      const userService = strapi.plugins['users-permissions'].services.user
      delete newUser.pBranchConfigs //清除租户配置，重新添加
      await userService.syncUserBranchConfigs(newUser, true);
    }
  }
}

module.exports = {
  syncUser
}
