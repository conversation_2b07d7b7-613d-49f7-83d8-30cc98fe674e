interface Model {
  // 数据源配置
  connection: string,
  collectionName: string,
  kind: 'collectionType' | 'singleType',
  orm: 'mongoose' | 'bookshelf',

  /**
   * 模型类型 - 普通或组件
   */
  modelType: 'contentType' | 'component',
  /**
   * 模型名称
   * @example book
   */
  modelName: string,

  /**
   * 模型全局 ID
   * @description 具有前缀的完整ID，可用于 findRelation 等功能
   * @example plugins::users-permissions.user - 可用于 findRelation
   */
  uid: string,
  /**
   * 模型 ID
   * @example user
   * @description 即模型名称，可能会重复的模型ID，例如不同插件中存在同命的模型名称
   */
  apiID: string,
  /**
   * 项目下模型来源目录名称
   */
  apiName: string,

  // ORM 对接
  globalId: string,
  globalName: string,

  /**
   * 主键字段
   */
  primaryKey: string,
  /**
   * 主键字段类型
   */
  primaryKeyType: 'string' | 'integer' | 'uuid',
  /**
   * 所有模型属性 - 包含自动生成的字段，例如时间戳
   */
  allAttributes: {
    [name: string]: ModelAttribute
  }

  /**
   * 是否在内容管理器中展示
   */
  isDisplayed: boolean,
  /**
   * 模型类型 - 集合或单例
   */

  /**
   * 模型信息 用来存储模型的展示型的信息字段
   */
  info: {
    // 模型名称
    name: string,
    // 模型描述
    description: string,
    // 模型展示名称
    label: string,
    // 模型展示图标 Component Model - 可使用 MaterialDesign 的图标
    icon?: string,
    // 模型版本
    version?: string,
  },
  /**
   * 模型基础配置信息 如自增、时间戳、发布控制等
   */
  options: {
    // 是否使用自增ID
    increments?: boolean,
    // 是否启用时间戳 @default false
    // 设置 ture 等同于 ["createdAt", "updatedAt"]
    timestamps?: boolean | [string, string],
    // 是否启用草稿系统 @default false
    draftAndPublish?: false,
    // 设置一组私有属性
    privateAttributes?: string[],
    // 配置API响应是否包含 created_by 和 updated_by 字段 @default false
    populateCreatorFields?: boolean,
    // 配置主字段 - 用于关联选择、默认排序等
    defaultMainField?: string,
    // 条件子模式 - 暂时实现根据一个字段的值做字段隐藏控制
    allOf?: {
      if: {
        properties: {
          [key: string]: {
            const: any,
          }
        }
      },
      then: {
        properties: {
          [key: string]: {
            visible: boolean,
          }
        }
      },
      else: {
        properties: {
          [key: string]: {
            visible: boolean,
          }
        }
      }
    }[],
    // 字段分组
    groups: {
      // 分组名称 - 无分组名称则只用来控制位置
      title?: string,
      // 分组字段列表
      fields: (string | object)[],
      // 分组默认展开
      defaultCollapsed?: boolean,
      // 分组聚合选项卡
      tabPanel: string
    }[]
  },
  /**
   * 模型属性字段 最重要的部分
   */
  attributes: {
    [name: string]: ModelAttribute
  },
  /**
   * 模型的目录 - 针对组件模型
   * @example 'block.audio'
   */
  category?: string
}

interface ModelAttributeBase {
  /**
   * 标签 - 可配置属性的中文名称
   */
  label?: string,
  /**
   * 默认值
   */
  default?: string,
  // 数据校验
  /**
   * 为 true 的情况下会增加一个校验器来检查此属性
   * @default false
   */
  required?: boolean,
  /**
   * 字段是否允许重复
   */
  unique?: boolean,
  // 安全配置
  /**
   * 是否可在 Strapi Content-type Builder plugin 中配置调整
   * @default true
   */
  configurable?: boolean,
  /**
   * 为 true 的情况下，通过 service 的查询会移除此属性
   * @default false
   */
  private?: boolean,
  /**
   * 为 false 的情况下，默认的 REST API 会不在查询此字段关联数据
   * @default true
   */
  autoPopulate?: boolean,
  // 表单默认配置
  /**
   * 是否可编辑
   * @default true
   */
  editable?: boolean,
  /**
   * 表单默认 placeholder
   */
  placeholder?: string,
  /**
   * 表单默认可见性
   * @default true
   */
  visible?: boolean,
  /**
   * 表单默认展示尺寸
   * @default true
   */
  size?: number,
  // 其他配置
  /**
   * 是否自动创建单字段索引 - 仅在 MongoDB 数据库支持
   * @default false
   */
  index?: boolean,
  /** 枚举选项配置 - 用于提供预制的选项组 */
  options?: {
    label: string,
    value: string
  }[] | string
  /**
   * 数据格式
   * @example { type: 'string', format: 'color'}  即代表当前字段是颜色字符串 "#FF00FF"
   */
  format?: 'color' | 'url' | 'secret' | string
  // 旧版本颜色字符串为 { type: 'string', color: true }
  /**
   * 组件扩展字段
   */
  meta?: {
    query?: Record<string, string | number>
  },
  /**
   * 可选数据来源接口
   */
  apiSearch?: string,
}

// 文本类型字段
interface ModelAttributeTypeText extends ModelAttributeBase {
  type: 'string' | 'text' | 'richtext',
  /**
   * 最大长度 - 针对字符串类型
   */
  maxLength?: number,
  /**
   * 最小长度 - 针对字符串类型
   */
  minLength?: number,
}

// 媒体字段类型
interface ModelAttributeTypeMedia extends ModelAttributeBase {
  type: 'media',
  model?: 'file',       // multiple = false
  collection?: 'file',  // multiple = true
  /**
   * 是否支支持多选
   */
  multiple?: boolean,
  allowedTypes?: ('images' | 'files' | 'videos')[],
  pluginOptions?: object,
}

// 数字字段类型
interface ModelAttributeTypeNumber extends ModelAttributeBase {
  type: 'integer' | 'biginteger' | 'float' | 'decimal' | 'number',
  /**
   * 最大值 - 针对数字类型
   */
  max?: number,
  /**
   * 最小值 - 针对数字类型
   */
  min?: number,
}

// 枚举字段类型
interface ModelAttributeTypeEnumeration extends ModelAttributeBase {
  type: 'enumeration',
  /**
   * 可用的枚举值 - 针对枚举类型
   */
  enum: string[],
}

// Uid 字段类型
// [ext] 目标模型的 uid - 非模型本身字段 model 接口根据其他关系配置字段生成
interface ModelAttributeTypeUid extends ModelAttributeBase {
  // uid 类型字段配置
  // 生成规则参数：https://github.com/sindresorhus/slugify
  type: 'uid',
  /**
   * 生成 uid 使用的字段
   */
  targetField?: string,
  /**
   * 配置 用户 uid 生成配置规则
   * @description uid生成配置 结果uid必须遵守以下 RegEx /^[A-Za-z0-9-_.~]*$/
   */
  options?: string,
}

// 关系字段类型
// [ext] 关系类型描述 - 非模型本身字段 model 接口根据其他关系配置字段生成
interface ModelAttributeTypeRelation extends ModelAttributeBase {
  type: 'relation',
  relationType: 'oneWay'
    | 'oneToOne' | 'oneToMany'
    | 'manyToOne' | 'manyToMany' | 'manyWay',
  // 关系类型
  model?: string,      // 关联单个的关系模型名称 与collection互斥
  collection?: string, // 关联多个的关系模型名称 与model互斥
  via?: string,        // via 为建立关联关系的模型字段
  /**
   * [ext] 目标模型的 uid - 非模型本身字段 model 接口根据其他关系配置字段生成
   * @example 'application::author.author'
   */
  targetModel?: string,
  /**
   * 多个关联模型的字段表单是否需要排序
   * @default false
   */
  multipleSort?: boolean,
  /**
   * 关联模型的主字段 - 用于列表、表单等展示
   */
  mainField?: string,
  /**
   * 关键数据查询筛选条件
   */
  query?: string,
}

interface JSONSchemaBasicOptions {
  // 基础注解
  type: string,         // 类型
  title?: string,       // 用户界面标题
  label?: string,       // title alias - deprecated - 仅为兼容旧版本
  description?: string, // 用户界面描述
  default?: any,        // 默认值
  examples?: any[],     // 示例值
  enum?: any[],         // 可用的枚举值
  const?: any,          // 固定值 - 功能类似单个枚举值
  format?: string,      // 格式 - e.g. 'date-time'
  // 可用 format
  // date-time : 日期时间 [type=string]
  // agent: 客户端信息 [type=object]
  // 其他字段
  // "allOf", "anyOf", "oneOf", "not", "if", "then", "else", "items", "additionalItems", "contains", "propertyNames", "properties", "patternProperties", "additionalProperties"
}

type JSONSchemaBasic = JSONSchemaBasicOptions &
  ({ // 字符串类型
    type: 'string',
    maxLength?: number,
    minLength?: number,
    pattern?: string,
    options?: { key: string, value: any }[],
  } | { // 数字类型
    type: 'number',
    maximum?: number,     // 最大值
    minimum?: number,     // 最小值
    multipleOf?: number,  // 倍数
    exclusiveMaximum?: number, // 排除最大值 - 配置任意数字即代表值 < maximum
    exclusiveMinimum?: number, // 排除最小值 - 配置任意数字即代表值 > minimum
    options?: { key: string, value: any }[],
  } | { // 布尔类型
    type: 'boolean',
  } | { // 其他类型
    type: 'null',
  })

// 对象类型
type JSONSchemaObject = JSONSchemaBasicOptions & {
  type: 'object',
  properties: {
    [key: string]: JSONSchema
  },

  // 校验字段
  required?: string[],  // 必须字段
  // 依赖必须字段 - 即一个字段存在需依赖其他字段存在
  // 例如：firstname 存在时，lastname 必须存在
  // dependentRequired: { "firstname": ["lastname"] }
  dependentRequired?: {
    [key: string]: string[] | {
      required: string[],
    }
  },
}

// 数组类型
type JSONSchemaArray = JSONSchemaBasicOptions & {
  type: 'array',
  items?: JSONSchema,          // 数组元素类型
  contains?: JSONSchema,       // 数据必须包含
  prefixItems?: JSONSchema[],  // 预设数组结构

  // 校验字段
  uniqueItems?: boolean,  // 是否允许重复
  maxItems?: number,      // 最大元素数量
  minItems?: number,      // 最小元素数量
  maxContains?: number,   // 包含 contains 元素的最大数量
  minContains?: number,   // 包含 contains 元素的最小数量
}

type JSONSchema = JSONSchemaBasic | JSONSchemaObject | JSONSchemaArray

// JSON类型字段
interface ModelAttributeTypeJson extends ModelAttributeBase {
  type: 'json',
  jsonSchema?: JSONSchema,

  // 扩展字段
  modelId?: string,       // 模型ID
  modelFields?: string[], // 模型应用字段
}

// 动态区域类型字段
interface ModelAttributeTypeDynamicZone extends ModelAttributeBase {
  type: 'dynamiczone',
  /**
   * 动态区域内支持的组件列表 - 例如 [ "user.address", "user.profile" ]
   */
  components?: string[],
  /**
   * 最大item数量， 默认999
   */
  maxItems?: number,
  /**
   * 最小item数量，默认 0
   */
  minItems?: number,
  /**
   * 组件是否可重复 - 此时值会是一个数组
   */
  repeatable?: boolean
  /**
   * 组件展示形式 。 默认值： "default" , 表格形式为：" "table", table 仅在动态区域为 components 为单模型时有效。
   */
  displayMode?: string,
  /**
   * 子表视图配置，仅在 displayMode 为 table 时有效
   */
  viewTableConfig: {
    /**
     * 默认值：取前五个基础类型数据列
     */
    columns?: string[],
    /**
     * [ create, batchEdit、batchDelete、edit、delete、show、copy、json ] ，
     * 默认值： [create, batchEdit、batchDelete、edit、delete、show]
     */
    functions?: string[]
  }
}

// 组件类型字段
interface ModelAttributeTypeComponent extends ModelAttributeBase {
  type: 'component',
  /**
   * 组件类型对应的组件名称
   * @example "user.profile"
   */
  component: string,
  /**
   * 最大item数量， 默认无限
   */
  maxItems?: number,
  /**
   * 最小item数量，默认 0
   */
  minItems?: number,
  /**
   * 组件是否可重复 - 此时值会是一个数组
   */
  repeatable?: boolean,
  /**
   * 组件展示形式 。 默认值： "default" , 表格形式为：" "table", table 仅在repeatable为 true 时有效。
   */
  displayMode?: string,
  /**
   * 子表视图配置，仅在 displayMode 为 table 时有效
   */
  viewTableConfig: {
    /**
     * 需要展示的列名称，如：[ 'id', 'name', 'label']
     * 默认值：取前五个基础类型数据列
     */
    columns?: string[],
    /**
     * [ create, batchEdit、batchDelete、edit、delete、show、copy、json ] ，
     * 默认值： [create, batchEdit、batchDelete、edit、delete、show]
     */
    functions?: string[]
  }
}

// 其他类型字段
interface ModelAttributeTypeOther extends ModelAttributeBase {
  type: 'email'        // [ ]
    | 'password'       // [ ]
    | 'date' | 'time'  // [ ]
    | 'datetime'       // [✅]
    | 'boolean'        // [✅]
}

// 模型属性
type ModelAttribute =
  ModelAttributeTypeOther
  | ModelAttributeTypeText
  | ModelAttributeTypeMedia
  | ModelAttributeTypeNumber
  | ModelAttributeTypeEnumeration
  | ModelAttributeTypeUid
  | ModelAttributeTypeRelation
  | ModelAttributeTypeJson
  | ModelAttributeTypeDynamicZone
  | ModelAttributeTypeComponent
  // 内置扩展类型
  | { type: 'timestamp' }
