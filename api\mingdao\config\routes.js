const { createDefaultRoutes } = require('accel-utils')
module.exports = {
  'routes': [
    ...createDefaultRoutes({
      basePath: '/mingdaoConfigs',
      controller: 'mingdao_config',
    }),
    ...createDefaultRoutes({
      basePath: '/mingdaoOrders',
      controller: 'mingdao_order',
    }),
    ...createDefaultRoutes({
      basePath: '/mingdaoRefunds',
      controller: 'mingdao_refund',
    }),
    ...createDefaultRoutes({
      basePath: '/mingdaoUsers',
      controller: 'mingdao_user',
      mode: 'branch'
    }),
    ...createDefaultRoutes({
      basePath: '/mingdaoUserTypes',
      controller: 'mingdao_usertype',
      mode: 'branch'
    }),
    ...createDefaultRoutes({
      basePath: '/mingdaoShareOrders',
      controller: 'mingdao_shareOrder',
      mode: 'branch'
    }),
    ...createDefaultRoutes({
      basePath: '/mingdaoClerks',
      controller: 'mingdao_clerk',
      mode: 'branch'
    }),
    ...createDefaultRoutes({
      basePath: '/vipOrders',
      controller: 'vip_order',
      mode: 'branch'
    }),
    ...createDefaultRoutes({
      basePath: '/mingdaoMenuConfigs',
      controller: 'mingdao_menuconfig',
      mode: 'branch'
    }),
    // 用户信息同步
    {
      'method': 'POST',
      'path': '/customer/syncUser',
      'handler': 'customer.syncUser',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    // 商品相关接口
    {
      'method': 'GET',
      'path': '/goods/getSkus',
      'handler': 'goods.getSkus',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/goods/getGoodList',
      'handler': 'goods.getGoodList',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    // 订单相关接口
    {
      'method': 'GET',
      'path': '/order/getDetail',
      'handler': 'order.getDetail',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/order/getDetailById',
      'handler': 'order.getDetailById',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/order/getOrderList',
      'handler': 'order.getOrderList',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    // 支付
    {
      "method": "POST",
      "path": "/pay/actions/createPayOrder",
      "handler": "pay.createPayOrder",
      "config": {
        "policies": []
      }
    },
    // H5支付
    {
      "method": "POST",
      "path": "/pay/actions/getH5PayParams",
      "handler": "pay.getH5PayParams",
      "config": {
        "policies": []
      }
    },
    // 退款
    {
      "method": "POST",
      "path": "/pay/actions/refund",
      "handler": "pay.refund",
      "config": {
        "policies": []
      }
    },
    // 微信支付回调
    {
      "method": "POST",
      "path": "/wechat/actions/callbackPayOrder/:id",
      "handler": "wechat_pay.callbackPayOrder",
      "config": {
        "policies": []
      }
    },
    {
      "method": "POST",
      "path": "/wechat/actions/notifyRefund/:id",
      "handler": "wechat_pay.notifyRefund",
      "config": {
        "policies": []
      }
    },
    // 通联支付回调
    {
      "method": "POST",
      "path": "/tonglian/actions/callbackPayOrder/:id",
      "handler": "tonglian_pay.callbackPayOrder",
      "config": {
        "policies": []
      }
    },
    // vip升级回调
    {
      "method": "POST",
      "path": "/vip/actions/callbackPayOrder/:id",
      "handler": "wechat_pay.callbackPayVip",
      "config": {
        "policies": []
      }
    },
    {
      "method": "GET",
      "path": "/order/getACodeUrl",
      "handler": "order.getACodeUrl",
      "config": {
        "policies": []
      }
    },
    {
      "method": "GET",
      "path": "/wechat/notice",
      "handler": "wechat.wechatNotice",
      "config": {
        "policies": []
      }
    },
    {
      "method": "POST",
      "path": "/mdy/mingdaoUser",
      "handler": "mingdao_user.fetchMdyUser",
      "config": {
        "policies": []
      }
    },
    {
      "method": "POST",
      "path": "/mdy/updateUser",
      "handler": "mingdao_user.fetchUpdateUser",
      "config": {
        "policies": []
      }
    },
    {
      "method": "POST",
      "path": "/uploads/images",
      "handler": "uploads.uploadImages",
      "config": {
        "policies": []
      }
    },
    {
      "method": "POST",
      "path": "/open/mingdaoShareOrders",
      "handler": "mingdao_shareOrder.setShareOrderList",
      "config": {
        "policies": []
      }
    },
    {
      "method": "GET",
      "path": "/open/mingdaoShareOrders",
      "handler": "mingdao_shareOrder.getShareOrderList",
      "config": {
        "policies": []
      }
    },
    {
      "method": "POST",
      "path": "/open/addClerkUser",
      "handler": "mingdao_user.addClerkUser",
      "config": {
        "policies": []
      }
    },
    {
      "method": "GET",
      "path": "/open/getClerkList",
      "handler": "mingdao_user.getClerkList",
      "config": {
        "policies": []
      }
    },
    {
      "method": "POST",
      "path": "/vip/pay",
      "handler": "pay.wxPay",
      "config": {
        "policies": []
      }
    },
    {
      "method": "GET",
      "path": "/open/appConfig",
      "handler": "mingdao_menuconfig.openGetAppConfig",
      "config": {
        "policies": []
      }
    }

  ]
}
