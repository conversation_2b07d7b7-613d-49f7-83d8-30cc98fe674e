'use strict';

module.exports = {
  "collectionName": "mingdao_usertype",
  "info": {
    "name": "mingdao_usertype",
    "label": "明道云用户类型配置",
    "description": "明道云用户类型配置"
  },
  "options": {
    "draftAndPublish": false,
    "timestamps": true
  },
  "pluginOptions": {},
  "attributes": {
    "name": {
      "label": "类型名称",
      "type": "string",
      required: true
    },
    type: {
      label: '类型',
      type: 'string',
      options: [
        {
          value: 'customer',
          label: '普通客户'
        },
        {
          value: 'admin',
          label: '系统管理员'
        }
      ],
      visible: true,
      required: true
    },
    account: {
      label: '登录明道云账号',
      type: 'string',
      visible: true,
      required: true

    },
    password: {
      label: '登录明道云密码',
      type: 'string',
      visible: true,
      required: true
    },
    accountType: {
      label: '账户类型',
      type: 'string',
      visible: false,
      options: [
        {
          value: 'email',
          label: '邮箱'
        },
        {
          value: 'phone',
          label: '电话号码'
        }
      ],
    },
    "pBranch": {
      "label": "租户",
      "plugin": "users-permissions",
      "model": "branch",
      "configurable": false
    },
  }
}
