// 应用
interface MingdaoApp {
  projectId: string;     // 网络ID？
  appId: string;         // 应用ID
  name: string;          // 应用名称
  iconUrl: string;       // 图标地址
  color: string;         // 图标颜色
  desc: string;          // 应用描述
  sections: MingdaoSection[];   // 应用分组
  createType: 0 | 1;     // 0 -普通？
}

// 模块/子模块
interface MingdaoSection {
  sectionId: string;    // 应用分组 Id
  name: string;         // 分组名称
  items: MingdaoSectionItem[];        // 分组下应用项
  childSections: MingdaoSection[]; // 子分组
}

// 菜单项
interface MingdaoSectionItem {
  id: string;           // 分组下应用项ID
                        // type=1 则ID为工作表ID
                        // type=2 则ID为页面ID
  name: string;         // 应用项名称
  type: number;         // 应用项类型 0:工作表、1:自定义页面、2：子分组
  iconUrl: string;      // 应用项图标地址
  status: number;       // 状态码？
  alias: string;        // 工作表别名
  notes: string;        // 开发者备注
}

// 工作表
interface MingdaoWorksheetData {
  worksheetId: string;   // 工作表ID
  name: string;          // 工作表名称
  views: MingdaoDataView[];     // 视图数组
  controls: MingdaoControl[];   // 控件数组
}

// 工作表视图
interface MingdaoDataView {
  viewId: string;  // 视图ID
  name: string;    // 视图名称
}

// 字段控件配置
interface MingdaoControlOption {
  value: string;   // 当type=11/10时,表示选项名称
  index: string;   // 排序
}

// 控件（字段）
interface MingdaoControl {
  // 基础属性
  controlId: string;              // 控件ID
  controlName: string;            // 控件名称
  type: string;                   // 控件类型，参考枚举 DataTypeEnum

  attribute: string;              // 属性 1：标题
  row: number;                    // 行号
  col: number;                    // 列号
  size: number;                   // 尺寸
  hint: string;                   // 引导文字
  default: string;                // 默认值
  dot?: string;                   // 当type=6时，表示保留小数位（0-14）
  unit?: string;                  // 单位，当type=46时，1：时分，6：时分秒
  enumDefault?: string;           // 1单选, 2多选
  enumDefault2?: string;          // 其他枚举默认值
  defaultMen: string[];           // 默认选中的人员

  dataSource: string;             // 源数据
  sourceControlId: string;        // 源控件id
  sourceControlType: string;      // 源控件类型
  sourceTitleControlId: string;   // 源标题控件id
  sourceEntityName: string,       // 源实体名称 eg.'记录'

  showControls: string[];         // 显示字段id

  noticeItem?: string;            // 当type=26时，通知项0：不通知 1：添加通知
  userPermission?: string;        // 当type=26时，权限 0：仅录入 1：成员  2：拥有者

  options?: MingdaoControlOption[];      // 控件选项

  required: string;               // true：必填,false：非必填
  half: boolean;                  // 是否占用半格
  relationControls: string[];     // 关联控件
  viewId: string;                 // 视图Id
  controlPermissions: string;     // 控件权限 "111" ？

  unique: boolean;                // 是否唯一
  coverCid: string;               // 封面控件id
  strDefault?: string;            // 通用string字段,参考说明
  desc: string;                   // 字段描述
  alias: string;                  // 别名（API用）
  remark: string;                 // 字段备注

  fieldPermission: string;        // 字段权限 "111"
  // 空或者 "111"，第一位能否查看，第二位能否编辑（只读），第三位能否添加； 1：能，0：不能
}
