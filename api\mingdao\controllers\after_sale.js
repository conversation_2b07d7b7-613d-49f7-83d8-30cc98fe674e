const mingdaoApi = require('../utils/mingdaoApi')
const worksheet = strapi.config.mingdao.worksheet

async function getDetailByAfterSaleNo(afterSaleNo, pBranchId) {
  const filters = [{
    'controlId': 'afterSaleNo',
    'dataType': 2,
    'spliceType': 1,
    'filterType': 2,
    'value': afterSaleNo
  }]
  let result = await mingdaoApi.getList(worksheet.sale, pBranchId, filters)
  let list = result && result.rows
  if (list && list.length) {
    return list[0];
  }
  return null
}

module.exports = {
  getDetailByAfterSaleNo
}
