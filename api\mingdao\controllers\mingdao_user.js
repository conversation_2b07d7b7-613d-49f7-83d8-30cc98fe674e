const { BranchCurdRouter } = require('accel-utils')
const { includes, size } = require("lodash");

const branchCurdRouter = new (class extends BranchCurdRouter {

  constructor(modelName) {
    super(modelName)
  }

  async _formatData(ctx) {
    const { mingdao_usertype } = ctx.request.body
    if (mingdao_usertype) {
      const usertype = await strapi.query('mingdao_usertype').findOne({ _id: mingdao_usertype })
      ctx.request.body.accountType = usertype.accountType
    }
    if (ctx.request.body.accountType === 'email' && !includes(ctx.request.body.name, '@')) {
      ctx.request.body.name = ctx.request.body.name + '@mmd.com'
    }
    // 生产手机号
    if (ctx.request.body.accountType === 'phone' && includes(ctx.request.body.name, '@')) {
      ctx.request.body.name = ctx.request.body.name.replace('@mmd.com', '')
    }
  }

  async branchCreate(ctx) {
    await this._formatData(ctx)
    // 判断是否是手机号
    return super.branchCreate(ctx);
  }

  async branchUpdate(ctx) {
    await this._formatData(ctx)
    return super.branchUpdate(ctx);
  }


})('mingdao_user')

module.exports = {
  ...branchCurdRouter.createHandlers(),

  async fetchMdyUser(ctx) {
    const { name, password } = ctx.request.body
    const user = await strapi.query('mingdao_user').findOne({ name, password })
    if (!user) {
      return {}
    }

    return user
  },

  async fetchUpdateUser(ctx) {
    const { account, oldPassword, newPassword, confirmPassword } = ctx.request.body
    const user = await strapi.query('mingdao_user').findOne({ name: account, password: oldPassword })
    if (!user) {
      return { code: 0, msg: '用户名或密码错误' }
    }
    await strapi.query('mingdao_user').update({ id: user.id }, { ...user, name: account, password: newPassword })
    return { code: 1, msg: '更新成功' }
  },

  // 获取列表
  async getClerkList(ctx) {
    const { relevanceId, id } = ctx.query
    if (id) {
      return await strapi.query('mingdao_user').findOne({ id })
    }
    const relevanceInfo = await strapi.query('mingdao_user').findOne({ id: relevanceId })
    if (!relevanceInfo) {
      return { code: 0, msg: 'success', data: [] }
    }
    const { mingdao_configs, type } = relevanceInfo
    return await strapi.query('mingdao_user').find({ mingdao_configs_eq: mingdao_configs.id, type: 'clerk' })

  },
  // 添加店员
  async addClerkUser(ctx) {
    const { relevanceId, name, id } = ctx.request.body
    try {
      if (id) {
        return await strapi.query('mingdao_user').update({ id }, ctx.request.body)
      }
      const mingdaoUser = await strapi.query('mingdao_user').findOne({ name })
      if (mingdaoUser) {
        return ctx.badRequest('手机号已存在', 'PHONE_EXISTS')
      }
      if (!relevanceId) {
        return ctx.badRequest('关联用户ID为空', 'RELEVANCE_ID_IS_NULL')
      }
      const relevanceInfo = await strapi.query('mingdao_user').findOne({ id: relevanceId })
      if (!relevanceInfo) {
        return ctx.badRequest('关联用户不存在', 'RELEVANCE_USER_NOT_EXISTS')
      }

      if (!size(relevanceInfo.associateClerk)) {
        return ctx.badRequest('该店员未关联店长', 'ASSOCIATE_CLERK_NOT_EXISTS')
      }

      if (relevanceInfo.type !== 'store') {
        return ctx.badRequest('店长类型错误', 'TYPE_ERROR')
      }

      ctx.request.body.type = 'clerk'
      ctx.request.body.accountType = 'phone'
      ctx.request.body.mingdao_configs = relevanceInfo.mingdao_configs
      ctx.request.body.mingdao_usertype = relevanceInfo.associateClerk
      ctx.request.body.pBranch = relevanceInfo.pBranch
      delete ctx.request.body.relevanceId
      if (ctx.request.body.sex === '') delete ctx.request.body.sex

      return await strapi.query('mingdao_user').create(ctx.request.body)
    } catch (error) {
      return ctx.badRequest(error)
    }

  }

}
