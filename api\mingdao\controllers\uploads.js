const { objectStorageConfig } = require('../../../plugins/upload/config/object-storage');
const { uploadFileToBucket } = require('../../../plugins/upload/services/object-storage');
const { genOcrImage, getAccessToken } = require('../../../plugins/users-permissions/utils/wechat');
const verifyData = strapi.config.get('plugins.upload.verifyData')


// async function genOcrImage({ appid, secret }, imgUrl) {
//   const accessToken = await getAccessToken({ appid, secret })
//   const res = await axios.post(
//     `https://api.weixin.qq.com/cv/ocr/comm?access_token=${accessToken}&img_url=${imgUrl}`,
//   )
//   const data = res.data

//   return data
// }

async function uploadImages(ctx) {

  const { files } = ctx.request
  const file = files.file
  if (!file) {
    // 如果没有传递图片URL，返回错误
    ctx.code = 400;
    ctx.body = { success: false, message: '缺少图片URL' };
    return;
  }
  const { name } = file
  const dir = [objectStorageConfig.baseDir, objectStorageConfig.uploadPath].filter(e => e).join('')
  let { url } = await uploadFileToBucket(name, file.path, dir)

  console.log("🚀 ~ uploadImages ~ verifyData:", verifyData)

  const ocrText = await genOcrImage(verifyData, url)
  if (ocrText.errcode !== 0) {
    return { success: false, message: 'OCR识别失败', code: 402 };
  }

  return { code: 0, data: ocrText.items, message: 'OCR识别成功' }
}

module.exports = {
  uploadImages
};
