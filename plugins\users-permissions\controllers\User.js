'use strict'

/**
 * User.js controller
 *
 * @description: A set of functions called "actions" for managing `User`.
 */

const _ = require('lodash')
const { sanitizeEntity } = require('accel-utils')
const apiUserController = require('./user/api')
const { BranchCurdRouter, CurdRouter, DomainCurdRouter } = require('accel-utils')
const { dataFieldProcess } = require('accel-utils/collection-utils')
const { omit, flatten, cloneDeep } = require('lodash')

class UserCurdRouter extends CurdRouter {
  constructor (name, config = {}) {
    super(name, config)
  }

  async create (ctx) {
    const userService = strapi.plugins['users-permissions'].services.user
    const body = ctx.request.body
    if (!body.role) {
      body.role = body.roles[0]
    }
    if (!body.role) {
      return ctx.badRequest('角色不允许为空（无角色用户无法正常访问系统）')
    }
    if (!body.pBranch) {
      return ctx.badRequest('当前租户不允许为空（租户为空会导致数据不进行租户过滤）')
    }
    if (!body.pBranches) {
      return ctx.badRequest('可用租户不允许为空（租户为空会导致数据不进行租户过滤）')
    }
    const { data, files } = this._parseCtx(ctx)
    try {
      await dataFieldProcess(data, this._getModel(ctx))
    } catch (e) {
      return ctx.badRequest(e.toString())
    }
    const entity = await this._getService(ctx).create({ data, files })
    const user = this._sanitizeEntity(ctx, entity)
    return await userService.syncUserBranchConfigs(user, true)
  }
}

const userRouter = new UserCurdRouter('user', { pluginName: 'users-permissions' })

class BranchUserCurdRouter extends BranchCurdRouter {
  constructor (modelName, config) {
    super(modelName, config)
  }

  async branchFind (ctx) {
    const entities = await super.branchFind(ctx)
    const { branchId } = this._parseBranchCtx(ctx)
    entities.forEach(entity => {
      const branchConfig = entity.pBranchConfigs?.find(e => e.branchId === branchId)
      if (branchConfig) {
        Object.assign(entity, _.omit(branchConfig, ['branchId']))
      }
    })
    // 补充角色数据
    const roles = await strapi.query('role', 'users-permissions').find({ _limit: -1 }, [])
    for (let entity of entities) {
      entity.roles = roles.filter(e => entity.roles.includes(e.id))
      entity.role = roles.find(e => e.id === entity.role)
    }
    return entities
  }

  async branchUpdate (ctx) {
    const { params, branchId, user } = this._parseBranchCtx(ctx)
    const userService = strapi.plugins['users-permissions'].services.user
    let targetUser = await userService.fetchAuthenticatedUser(params.id)
    targetUser = await userService.syncUserBranchConfigs(targetUser, true)
    // 同步修改租户下配置
    const body = ctx.request.body
    if (branchId) {
      for (const pBranchConfig of targetUser.pBranchConfigs) {
        if (pBranchConfig.branchId === user.pBranch.id) {
          // 租户内用户角色
          if (body.role) {
            pBranchConfig.role = body.role
          }
          if (body.roles) {
            pBranchConfig.roles = body.roles
          }
          // 租户内用户基础信息
          Object.assign(pBranchConfig, _.pick(body, ['username', 'phone', 'email', 'blocked']))
        }
      }
      if (targetUser.pBranch.id === branchId) {
        ctx.request.body = {
          role: body.role,
          roles: body.roles,
          pBranchConfigs: targetUser.pBranchConfigs
        }
      } else {
        ctx.request.body = {
          pBranchConfigs: targetUser.pBranchConfigs
        }
      }
    }
    return super.branchUpdate(ctx)
  }

  async branchCreate (ctx) {
    const userService = strapi.plugins['users-permissions'].services.user
    const body = ctx.request.body
    if (!body.role) {
      body.role = body.roles[0]
    }
    const user = await super.branchCreate(ctx)
    return await userService.syncUserBranchConfigs(user, true)
  }
}

const branchUserRouter = new BranchUserCurdRouter('user', { pluginName: 'users-permissions' })

const domainCurdRouter = new (class extends DomainCurdRouter {
  constructor (name, config = {}) {
    super(name, config)
  }

  async domainCreate (ctx) {
    const userService = strapi.plugins['users-permissions'].services.user
    const body = ctx.request.body
    if (!body.role) {
      body.role = body.roles[0]
    }
    if (!body.role) {
      return ctx.badRequest('角色不允许为空（无角色用户无法正常访问系统）')
    }
    const user = await super.domainCreate(ctx)
    return await userService.syncUserBranchConfigs(user, true)
  }
})('user', { pluginName: 'users-permissions' })

const resolveControllerMethod = method => ctx => {
  const callbackFn = apiUserController[method]

  if (!_.isFunction(callbackFn)) {
    return ctx.notFound()
  }

  return callbackFn(ctx)
}

module.exports = {
  create: resolveControllerMethod('create'),
  update: resolveControllerMethod('update'),

  /**
   * Retrieve authenticated user.
   * @return {Object|Array}
   */
  async me (ctx) {
    const user = ctx.state.user
    if (!user) {
      throw strapi.errors['unauthorized']('Token invalid')
    }
    const iPlugin = strapi.plugins['users-permissions']
    const originUser = await iPlugin.services['user'].findOne({ id: user.id })
    let remoteUser = _.cloneDeep(originUser)
    if (user.pBranch && user.pBranchConfigs) {
      const branchConfig = user.pBranchConfigs?.find(e => e.branchId === user.pBranch.id)
      if (branchConfig) {
        Object.assign(remoteUser, _.omit(branchConfig, ['branchId', 'role', 'roles']))
        // 增加临时机制 - 解决小程序绑定手机号被租户内配置覆盖获取不到手机号的问题
        remoteUser.phone = remoteUser.phone || originUser.phone
      }
    }
    // 字段过滤
    let sanitizeUser = sanitizeEntity(remoteUser, {
      model: strapi.query('user', 'users-permissions').model,
    })
    sanitizeUser?.thirdParties.forEach(thirdParty => {
      delete thirdParty.app
      delete thirdParty.account
    })

    // 用户权限菜单模块
    const getUserModules = async user => {
      const roleId = user.role
      const role = await strapi.query('role', 'users-permissions').findOne({ id: roleId }, ['modules'])
      const modules = await strapi.query('group', 'users-permissions').find({ id_in: role.modules }, ['pages'])
      const apps = await strapi.query('app', 'users-permissions').find({}, ['modules'])
      const userModules = modules.map(e => {
        return {
          id: e.id,
          sId: e.sId,
          name: e.name,
          cover: e.cover,
          viewPermissions: e.viewPermissions,
          pages: e.pages?.map(e => omit(e, ['configs'])),
          type: e.type,
        }
      })
      // 获取三级域名并过滤功能模块
      // Origin: http://zhxy-survey.yunxiao.localhost:8018
      const origin = ctx.accept.headers.origin
      const getThirdLevelDomain = url => {
        const urlObject = new URL(url)
        const hostnameParts = urlObject.hostname.split('.')
        if (hostnameParts.length < 3) {
          return ''
        }
        return hostnameParts[hostnameParts.length - 3]
      }
      const thirdLevelDomain = getThirdLevelDomain(origin)
      if (thirdLevelDomain) {
        const app = apps.find(e => e.thirdLevelDomain === thirdLevelDomain)
        if (app) {
          return userModules.filter(e => app.modules.map(e => e.sId).includes(e.sId))
        }
      }
      return userModules
    }
    const userModules = await getUserModules(user)
    const findUserViewBySId = sId => {
      const allViewPermissions = flatten(userModules.map(e => e.pages).filter(e => !!e))
      if (!sId) return
      return allViewPermissions.find(e => e.sId === sId)
    }
    const pluginStore = strapi.store({ type: 'plugin', name: 'users-permissions' })
    const menus = await pluginStore.get({ key: 'menus' })
    let userMenus = cloneDeep(menus)
    for (const userMenu of userMenus) {
      userMenu.pages = userMenu.pages.filter(e => findUserViewBySId(e.sId))
    }
    userMenus = userMenus.filter(e => e.pages.length > 0)
    return {
      ...sanitizeUser,
      menus: userMenus,
      modules: userModules
    }
  },

  async updateMe (ctx) {
    const user = ctx.state.user
    const userPlugin = strapi.plugins['users-permissions']
    const body = ctx.request.body
    if (!user) {
      return ctx.badRequest(null, [{ messages: [{ id: 'No authorization header was found' }] }])
    }
    if (user.pBranch.id) {
      for (const pBranchConfig of user.pBranchConfigs) {
        if (pBranchConfig.branchId === user.pBranch.id) {
          // 租户内用户基础信息
          Object.assign(pBranchConfig, _.pick(body, ['username', 'phone', 'email', 'blocked']))
        }
      }
      body.pBranchConfigs = user.pBranchConfigs
    }
    const newUser = await userPlugin.services.user.updateOne(user.id, _.pick(body, [
      'username', 'avatar', 'description', 'pBranchConfigs'
    ]))
    return _.pick(newUser, ['id', 'username', 'avatar', 'description', 'pBranchConfigs'])
  },

  async changeCurrentRole (ctx) {
    let user = ctx.state.user
    const { roleId } = ctx.request.body
    const userService = strapi.plugins['users-permissions'].services.user
    let currentUser = await userService.fetchAuthenticatedUser(user.id)
    const matchRole = currentUser.roles?.find(id => id.toString() === roleId || id?.id.toString() === roleId)
    if (!matchRole) {
      return ctx.badRequest('Role match error.')
    }

    // 历史数据兼容处理 - 生成各租户配置数据
    const userPlugin = strapi.plugins['users-permissions']
    currentUser = await userPlugin.services['user'].syncUserBranchConfigs(user, true)

    if (currentUser.pBranchConfigs) {
      // 修改租户下配置信息
      for (const pBranchConfig of currentUser.pBranchConfigs) {
        if (pBranchConfig.branchId === currentUser.pBranch.id) {
          pBranchConfig.role = matchRole.id
          pBranchConfig.roles = currentUser.roles.map(e => e.id)
        }
      }
    } else {
      // 超管无租户配置信息
    }

    await strapi.query('user', 'users-permissions').update({ id: currentUser.id }, {
      role: matchRole,
      pBranchConfigs: currentUser.pBranchConfigs
    })
    return {
      role: matchRole,
      roles: currentUser.roles
    }
  },

  async changeCurrentBranch (ctx) {
    let user = ctx.state.user
    const { branchId } = ctx.request.body
    const matchBranch = user.pBranches?.find(branch => branch.id === branchId)
    if (!matchBranch) {
      return ctx.badRequest('Branch match error.')
    }

    // 历史数据兼容处理 - 生成各租户配置数据
    const userPlugin = strapi.plugins['users-permissions']
    user = await userPlugin.services['user'].syncUserBranchConfigs(user, true)

    let matchBranchConfig = user.pBranchConfigs?.find(branchConfig => branchConfig.branchId === branchId)
    if (!matchBranchConfig) {
      return ctx.badRequest('Branch config match error.')
    }
    // 更新用户到对应租户可用角色
    const roleUpdate = {}
    if (matchBranchConfig) {
      if (matchBranchConfig.role) {
        roleUpdate.role = matchBranchConfig.role
      }
      if (matchBranchConfig.roles) {
        roleUpdate.roles = matchBranchConfig.roles
      }
    }
    // 更新租户、当前角色、角色列表
    await strapi.query('user', 'users-permissions').update({ id: user.id }, {
      pBranch: matchBranch.id,
      ...roleUpdate
    })
    return {
      pBranch: matchBranch.id,
      pBranches: user.pBranches,
      pBranchConfigs: user.pBranchConfigs,
    }
  },
  ...userRouter.createHandlers(),
  ...branchUserRouter.createHandlers(),
  ...domainCurdRouter.createHandlers(),
}
