const WxPay = require('wechatpay-node-v3')
const axios = require("axios")
const { notifyMingdaoPaySuccess, notifyMingdaoRefund } = require('./order')
const uuid = require('uuid')
const mingdaoApi = require('../utils/mingdaoApi')
const { head } = require('lodash')
const dayjs = require('dayjs')

// let wxPayConfig = null

// 生成商户订单号（UUID）
function _genOrderUUID() {
  return uuid.v4().split('-').join('');               // 商户系统内部的订单号,32个字符内、可包含字母
}
// async function getWxPayConfig(){
//   if (wxPayConfig) {
//     return wxPayConfig
//   }
//   let res = await axios.get(`${strapi.config.server.config.url}/pay-wechat/publicFind`, {
//     headers: {
//       'access-key': strapi.config.server.config.accessKey
//     }
//   })
//   const wechatPayConfigList = res.data
//   if (wechatPayConfigList && wechatPayConfigList.length > 0) {
//     wxPayConfig = wechatPayConfigList[0]
//   }
//   return wxPayConfig;
// }

let wxPay = null
// 订单支付状态 payStatus
// 0 - 未支付
// 1 - 支付中
// 2 - 支付完成
// -1 - 支付失败

async function getWxPay(app) {
  if (wxPay) {
    return wxPay
  }
  const publicKey = await axios.get(app.certPem.url, { responseType: 'arraybuffer' })
  const privateKey = await axios.get(app.keyPem.url, { responseType: 'arraybuffer' })

  wxPay = new WxPay({
    appid: app.appId,
    mchid: app.merchantId,
    publicKey: publicKey.data, // 证书
    privateKey: privateKey.data, // 秘钥
    key: app.apiKey
  })
  return wxPay
}

// 1. 小程序创建支付单
async function createPayOrder(ctx, mingdaoConfig, orderNo, amountPrice, openid, payChannel) {
  // https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_5_1.shtml
  // Request
  // - out_trade_no：商户系统内部订单号，只能是数字、大小写字母_-*且在同一个商户号下唯一
  // - description：商品描述
  // - notify_url：支付回调通知URL，该地址必须为直接可访问的URL，不允许携带查询串
  // - total：订单总金额，单位为分
  // - openid：openid是微信用户在appid下的唯一用户标识（appid不同，则获取到的openid就不同），可用于永久标记一个用户。openid获取方式请参考以下文档 小程序获取openid、 公众号获取openid、 APP获取openid、
  // Response
  // - {
  // 	"prepay_id": "wx26112221580621e9b071c00d9e093b0000"
  // }

  let outTradeNo = _genOrderUUID();
  const params = {
    description: `${mingdaoConfig.name}-${orderNo}`,
    out_trade_no: outTradeNo,
    attach: orderNo,
    notify_url: `${strapi.config.server.serverWanUrl}/wechat/actions/callbackPayOrder/${mingdaoConfig.id}`,
    amount: { total: amountPrice, },
    payer: { openid: openid, },
    scene_info: { payer_client_ip: 'ip', },
  }

  const wechatPayConfig = mingdaoConfig.wx_pay;
  const pBranchId = mingdaoConfig.pBranch.id;
  if (!wechatPayConfig) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '微信支付配置异常')
  }

  const wxPay = await getWxPay(wechatPayConfig)

  const payInfo = await wxPay.transactions_jsapi(params)
  if (payInfo.status !== 200) {
    return ctx.wrapper.error('HANDLE_ERROR', payInfo.error || '微信支付异常')
  }
  await strapi.services['mingdao_order'].create({
    order_no: outTradeNo,
    mingdao_order_no: orderNo,
    pay_status: 0,
    order_price: amountPrice,
    payment_type: 0,
    payment_channel: payChannel,
    payment_merchant: wechatPayConfig.merchantId,
    pBranch: pBranchId,
  })

  // // 小程序调起支付参数
  // const signType = 'RSA'
  // const appId = APP_ID
  // const timeStamp = parseInt(+new Date() / 1000 + '').toString()
  // const nonceStr = randomString(32)
  // const orderPackage = `prepay_id=${prepayId}`
  // const paySign = wxPay.sha256WithRsa(`${appId}\n${timeStamp}\n${nonceStr}\n${orderPackage}`)
  return ctx.wrapper.succ({
    orderId: outTradeNo,
    payType: 0,
    payInfo
  })
}

// 2.【客户端】小程序调起支付API
// https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_5_4.shtml
async function wxVipPay(ctx, mingdaoConfig, openid, appleInfo) {
  const { userId, amountPrice } = ctx.request.body
  let outTradeNo = _genOrderUUID();

  const wechatPayConfig = head(mingdaoConfig.data)
  if (!wechatPayConfig) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '微信支付配置异常')
  }


  const params = {
    description: `vip-升级订单`,
    out_trade_no: outTradeNo,
    attach: appleInfo.id,
    notify_url: `${strapi.config.server.serverWanUrl}/vip/actions/callbackPayOrder/${wechatPayConfig.id}`,
    amount: { total: (+amountPrice * 100), },
    payer: { openid: openid, },
    scene_info: { payer_client_ip: 'ip', },
  }

  const wxPay = await getWxPay(wechatPayConfig)

  const payInfo = await wxPay.transactions_jsapi(params)
  console.info("🚀 ~ wxVipPay ~ payInfo:", payInfo)
  if (payInfo.status !== 200) {
    return ctx.wrapper.error('HANDLE_ERROR', payInfo.error || '微信支付异常')
  }

  const result = payInfo.data || payInfo
  console.info("🚀 ~ wxVipPay ~ result:", result)

  const payParams = {
    appId: result.appId,
    timeStamp: String(Math.floor(Date.now() / 1000)),
    nonceStr: result.nonceStr, // 随机字符串
    package: `${result.package}`, // 预支付交易会话标识
    signType: result.signType, // 使用 RSA 签名
    order_no: outTradeNo,
  };

  // 生成签名
  const signature = wxPay.sign(
    `${payParams.appId}\n${payParams.timeStamp}\n${payParams.nonceStr}\n${payParams.package}\n`,
    '你的商户私钥'
  );

  payParams.paySign = signature;

  return payParams
}

// 3. 接收支付结果通知
// https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_5_5.shtml
/**
 *
 * @param ctx
 * @param ctx.request
 * @param ctx.request.body
 * @param ctx.request.body.resource
 * @param {string} ctx.request.body.resource.ciphertext
 * @param {string} ctx.request.body.resource.associated_data
 * @param {string} ctx.request.body.resource.nonce
 */
async function callbackPayOrder(ctx) {
  console.log('----notify---', ctx.request.origin)
  const { id } = ctx.params
  const mingdaoConfig = await mingdaoApi.getMingdaoConfig(id);

  const wechatPayConfig = mingdaoConfig.wx_pay;
  const pBranchId = mingdaoConfig.pBranch.id;
  if (!wechatPayConfig) {
    return ctx.wrapper.sendError({ 'code': 'FAIL', 'message': '微信支付配置异常' })
  }

  const wxPay = await getWxPay(wechatPayConfig)

  const data = ctx.request.body
  const resource = data.resource
  console.log('----notify2----', data)
  try {
    // 通知参数
    // id - 通知的唯一ID 示例值：EV-2018022511223320873
    // create_time - 通知创建时间	示例值：2015-05-20T13:29:35+08:00
    // event_type - 通知的类型 支付成功通知的类型为 TRANSACTION.SUCCESS 示例值：TRANSACTION.SUCCESS
    // resource_type - 通知数据类型 支付成功通知为 encrypt-resource 示例值：encrypt-resource
    // resource - 通知数据	json格式 - 为加密数据，需要解密
    // summary - 回调摘要 示例值：支付成功

    // 解密后的 resource
    const payOrder = wxPay.decipher_gcm(resource.ciphertext, resource.associated_data, resource.nonce, wechatPayConfig.apiKey)
    // 字段说明
    // payOrder.appid
    // payOrder.mchid
    // payOrder.out_trade_no
    // payOrder.trade_state
    //  交易状态，枚举值：
    //  SUCCESS：支付成功
    //  REFUND：转入退款
    //  NOTPAY：未支付
    //  CLOSED：已关闭
    //  REVOKED：已撤销（付款码支付）
    //  USERPAYING：用户支付中（付款码支付）
    //  PAYERROR：支付失败(其他原因，如银行返回失败)
    if (payOrder.trade_state === 'SUCCESS') {
      const { out_trade_no, transaction_id, success_time, attach, amount } = payOrder
      // 支付成功后更新 明道云订单状态
      await strapi.services['mingdao_order'].update({
        pBranch: pBranchId,
        order_no: out_trade_no
      }, {
        pay_status: 1,
        transaction_id: transaction_id,
        payment_time: new Date(success_time),
        payInfo: payOrder,
      })
      // orderNo: attach //附加数据为明道云订单号
      // payment_time
      // transaction_id
      // pay_type
      // pay_amount
      console.log('----notify3----', payOrder)
      notifyMingdaoPaySuccess(attach, pBranchId, success_time, transaction_id, '微信支付', amount.total)
    }
  } catch (e) {
    console.log('Error:', e)
    return ctx.wrapper.sendError({ 'code': 'FAIL', 'message': e.toString() })
  }
  return { 'code': 'SUCCESS', 'message': '' }
}

async function callbackPayVip(ctx) {
  const { id } = ctx.params
  console.log('----notify---', ctx.request.origin, id)
  const mingdaoConfig = await getConfigPay(id);

  const wechatPayConfig = head(mingdaoConfig.data)
  // const pBranchId = pBranch;
  if (!wechatPayConfig) {
    return ctx.wrapper.sendError({ 'code': 'FAIL', 'message': '微信支付配置异常' })
  }

  const wxPay = await getWxPay(wechatPayConfig)

  const data = ctx.request.body
  const resource = data.resource
  console.log('----notify2----', data)
  try {
    // 通知参数
    // id - 通知的唯一ID 示例值：EV-2018022511223320873
    // create_time - 通知创建时间	示例值：2015-05-20T13:29:35+08:00
    // event_type - 通知的类型 支付成功通知的类型为 TRANSACTION.SUCCESS 示例值：TRANSACTION.SUCCESS
    // resource_type - 通知数据类型 支付成功通知为 encrypt-resource 示例值：encrypt-resource
    // resource - 通知数据	json格式 - 为加密数据，需要解密
    // summary - 回调摘要 示例值：支付成功

    // 解密后的 resource
    const payOrder = wxPay.decipher_gcm(resource.ciphertext, resource.associated_data, resource.nonce, wechatPayConfig.apiKey)
    console.info("🚀 ~ callbackPayVip ~ payOrder:", payOrder)
    // const payOrder = {
    //   mchid: '**********',
    //   appid: 'wx7090745609fd7d58',
    //   out_trade_no: 'b4492691002c4d2084b41fae6931ef58',
    //   transaction_id: '4200002370202410163677779321',
    //   trade_type: 'JSAPI',
    //   trade_state: 'SUCCESS',
    //   trade_state_desc: '支付成功',
    //   bank_type: 'OTHERS',
    //   attach: '66b34c6ecff10703a4b8f2a9',
    //   success_time: '2024-10-16T10:22:43+08:00',
    //   payer: {
    //     openid: 'o9AQu5TlG7mtJY8ks91G_j-bzVVo'
    //   },
    //   amount: {
    //     total: 3,
    //     payer_total: 3,
    //     currency: 'CNY',
    //     payer_currency: 'CNY'
    //   }
    // }
    // 字段说明
    // payOrder.appid
    // payOrder.mchid
    // payOrder.out_trade_no
    // payOrder.trade_state
    //  交易状态，枚举值：
    //  SUCCESS：支付成功
    //  REFUND：转入退款
    //  NOTPAY：未支付
    //  CLOSED：已关闭
    //  REVOKED：已撤销（付款码支付）
    //  USERPAYING：用户支付中（付款码支付）
    //  PAYERROR：支付失败(其他原因，如银行返回失败)
    if (payOrder.trade_state === 'SUCCESS') {
      const { out_trade_no, transaction_id, success_time, attach, amount } = payOrder
      // 支付成功后更新 明道云订单状态
      // const vipOrder = await strapi.services['vip_order'].findOne({
      //   order_no: out_trade_no
      // })
      // const dateString = success_time.replace(/\s+/g, ''); // 去除所有空格
      await strapi.services['vip_order'].update({
        order_no: out_trade_no
      }, {
        paymentStatus: 1,
        transaction_id: transaction_id,
        paymentTime: success_time,
        payInfo: payOrder,
      })
      // orderNo: attach //附加数据为明道云订单号
      // payment_time
      // transaction_id
      // pay_type
      // pay_amount

      const mingdaoUser = await strapi.services['mingdao_user'].find({
        mingdao_configs_eq: [attach]
      })
      console.info('----notify3----', mingdaoUser)

      // total: amount['total'],
      // refund: amount['refund'],
      // payer_total: amount['payer_total'],
      const curVipTime = new Date()

      for (const mingdaoUserItem of mingdaoUser) {
        let vip = ''
        let clerkNumber = 0

        let curTime = mingdaoUserItem.vip || new Date()
        if (dayjs(curTime).isBefore(curVipTime)) {
          curTime = curVipTime
        }
        if (amount.payer_total === 49900 || amount.payer_total === 1) {
          vip = dayjs(curTime).add(1, 'year').toDate()
          clerkNumber = 1
        }
        if (amount.payer_total === 119900 || amount.payer_total === 2) {
          vip = dayjs(curTime).add(3, 'year').toDate()
          clerkNumber = 2
        }
        if (amount.payer_total === 149900 || amount.payer_total === 3) {
          vip = dayjs(curTime).add(5, 'year').toDate()
          clerkNumber = 3
        }

        const params = {
          vip
        }
        if (mingdaoUserItem.type === 'store') {
          params.clerkNumber = clerkNumber
        }
        await strapi.services['mingdao_user'].update({
          id: mingdaoUserItem.id
        }, params)
      }



    }
  } catch (e) {
    console.log('Error:', e)
    return ctx.wrapper.sendError({ 'code': 'FAIL', 'message': e.toString() })
  }
  return { 'code': 'SUCCESS', 'message': '' }
}

// https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_5_2.shtml
async function checkPayOrder(ctx) {
  const { id } = ctx.query
  if (!id) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误，id 不能为空')
  }
  // const wechatPayConfig = await getWxPayConfig();
  if (!wechatPayConfig) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '微信支付配置异常')
  }

  const pay = await getWxPay(wechatPayConfig)
  return await pay.query({ out_trade_no: id })
}

async function refund(ctx, mingdaoConfig, order, refund_id, amount, reason) {
  console.log('---- wechat refund ----')

  const wechatPayConfig = mingdaoConfig.wx_pay;
  const pBranchId = mingdaoConfig.pBranch.id;
  if (!wechatPayConfig) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '微信支付配置异常')
  }

  let out_refund_no = _genOrderUUID() //自己生成退款单号
  let params = {
    out_trade_no: order.order_no,//原订单号
    out_refund_no: out_refund_no,
    notify_url: `${strapi.config.server.serverWanUrl}/wechat/actions/notifyRefund/${mingdaoConfig.id}`,
    reason: reason,
    amount: {
      refund: amount,
      total: order.order_price,
      currency: 'CNY'
    }
  }

  const pay = await getWxPay(wechatPayConfig)

  const result = await pay.refunds(params);
  console.log('---- wechat refund ----', result)

  if (result.status !== 200) {
    await strapi.services['mingdao_refund'].create({
      refund_no: out_refund_no,
      third_refund_no: refund_id,
      order: order.id,
      refund: amount,
      refund_status: 2,
      refund_reason: reason,
      fail_reason: result.error || '微信退款失败',
      refundParams: params,
      refundInfo: result,
      pBranch: pBranchId,
    })
    notifyMingdaoRefund(
      refund_id,
      pBranchId,
      null,
      null,
      null,
      'ABNORMAL',
      result.error || '微信退款失败')
    return ctx.wrapper.error('HANDLE_ERROR', result.error || '微信退款失败')
  }

  await strapi.services['mingdao_refund'].create({
    refund_no: out_refund_no,
    third_refund_no: refund_id,
    order: order.id,
    refund: amount,
    refund_status: 0,
    refund_reason: reason,
    refundParams: params,
    pBranch: pBranchId,
  })

  return ctx.wrapper.succ(result);
}
async function notifyRefund(ctx) {
  console.log('----notify---', ctx.request.origin)
  const { id } = ctx.params
  const mingdaoConfig = await mingdaoApi.getMingdaoConfig(id);

  const wechatPayConfig = mingdaoConfig.wx_pay;
  const pBranchId = mingdaoConfig.pBranch.id;
  if (!wechatPayConfig) {
    return { 'code': 'FAIL', 'message': '微信支付配置异常' }
  }

  const wxPay = await getWxPay(wechatPayConfig)
  const data = ctx.request.body
  const resource = data.resource
  console.log('----notify2----', data)

  try {
    // 通知参数
    // id - 通知的唯一ID 示例值：EV-2018022511223320873
    // create_time - 通知创建时间	示例值：2015-05-20T13:29:35+08:00
    // event_type - 通知的类型 支付成功通知的类型为 TRANSACTION.SUCCESS 示例值：TRANSACTION.SUCCESS
    // resource_type - 通知数据类型 支付成功通知为 encrypt-resource 示例值：encrypt-resource
    // resource - 通知数据	json格式 - 为加密数据，需要解密
    // summary - 回调摘要 示例值：支付成功

    // 解密后的 resource
    const refundInfo = wxPay.decipher_gcm(resource.ciphertext, resource.associated_data, resource.nonce, wechatPayConfig.apiKey)
    // 字段说明
    // payOrder.appid
    // payOrder.mchid
    // payOrder.out_refund_no
    // payOrder.refund_id
    // payOrder.refund_status
    // 退款状态，枚举值：
    // SUCCESS：退款成功
    // CLOSED：退款关闭
    // ABNORMAL：退款异常，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，可前往【商户平台—>交易中心】，手动处理此笔退款
    console.log('----notify3----', refundInfo)

    const { refund_status, out_refund_no, refund_id, success_time, user_received_account, amount } = refundInfo
    const refundRecord = await strapi.services['mingdao_refund'].findOne({
      refund_no: out_refund_no,
      pBranch: pBranchId,
    })
    await strapi.services['mingdao_refund'].update({
      pBranch: pBranchId,
      refund_no: out_refund_no // 商户退款单号
    }, {
      refund_status: refund_status === 'SUCCESS' ? 1 : (refund_status === 'CLOSED' ? 3 : 2),
      transaction_id: refund_id,
      refund_time: success_time,
      user_received_account: user_received_account,
      total: amount['total'],
      refund: amount['refund'],
      payer_total: amount['payer_total'],
      payer_refund: amount['payer_refund'],
      refundInfo: refundInfo
    })
    // 退款后更新 明道云售后状态
    // afterSaleNo
    // refund_time
    // refund_trx_id
    // refund_amount
    // refund_status
    // refund_fail_reason

    notifyMingdaoRefund(
      refundRecord.third_refund_no,
      pBranchId,
      success_time,
      refund_id,
      amount['refund'],
      refund_status,
      '')
  } catch (e) {
    console.log('Error:', e)
    return { 'code': 'FAIL', 'message': e.toString() }
  }
  return { 'code': 'SUCCESS', 'message': '' }

}

const getConfigPay = async (id) => {

  try {
    const config = strapi.config.server.config
    return await axios.get(`${config.url}/pay-wechat/publicFind`, {
      headers: {
        'access-key': config.accessKey,
      },
      params: {
        id
      }
    })
  } catch (error) {
    return error
  }

}

module.exports = {
  createPayOrder,
  callbackPayOrder,
  // checkPayOrder,
  refund,
  notifyRefund,
  getConfigPay,
  wxVipPay,
  callbackPayVip

}
