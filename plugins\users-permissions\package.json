{"name": "strapi-plugin-users-permissions", "version": "3.6.8", "description": "Protect your API with a full-authentication process based on JWT", "strapi": {"name": "Roles & Permissions", "icon": "users", "description": "users-permissions.plugin.description", "required": true}, "typings": "./index", "scripts": {"test": "echo \\\"no tests yet\\\""}, "dependencies": {"@buffetjs/core": "3.3.8", "@buffetjs/custom": "3.3.8", "@buffetjs/hooks": "3.3.8", "@buffetjs/icons": "3.3.8", "@buffetjs/styles": "3.3.8", "@buffetjs/utils": "3.3.8", "@purest/providers": "^1.0.2", "bcryptjs": "^2.4.3", "grant-koa": "5.4.8", "immutable": "^3.8.2", "jsonwebtoken": "^8.1.0", "koa2-ratelimit": "^0.9.0", "lodash": "4.17.21", "purest": "3.1.0", "request": "^2.83.0", "uuid": "^3.1.0"}, "devDependencies": {"koa": "^2.13.1"}, "author": {"name": "Strapi team", "email": "<EMAIL>", "url": "https://strapi.io"}, "maintainers": [{"name": "Strapi team", "email": "<EMAIL>", "url": "https://strapi.io"}], "repository": {"type": "git", "url": "git://github.com/strapi/strapi.git"}, "engines": {"node": ">=16.20.0", "npm": ">=6.0.0"}, "license": "SEE LICENSE IN LICENSE", "gitHead": "231263a3535658bab1e9492c6aaaed8692d62a53"}