function queries ({ model, strapi }) {
  void strapi // unused

  async function find (params) {
    return await model.mingdaoFind(params)
  }

  async function count (params) {
    return await model.mingdaoCount(params)
  }

  async function create (values) {
    return await model.mingdaoCreate(values)
  }

  async function update (params, values) {
    return await model.mingdaoUpdate(params, values)
  }

  async function findOne (params) {
    return await model.mingdaoFindOne(params)
  }

  async function deleteMany (params) {
    return await model.mingdaoDeleteMany(params)
  }

  return {
    find,
    count,
    create,
    update,
    findOne,
    delete: deleteMany
  }
}

module.exports = queries
