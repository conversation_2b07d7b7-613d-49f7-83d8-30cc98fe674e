'use strict'

const { jwtGen, jwtVerify, sha1 } = require('accel-utils')
const { getWechatAuthData, getPhoneNumber, genQRCodeTicket, getOffiaccountUserInfo, genUrlLink, genMiniprogramQRCode, genMiniprogramACode,
  getOfficialAuthData,
  getOffiaccountJSTicket
} = require('../utils/wechat')
const { fmtNormalXML, genRandomString } = require('../utils/utils')
const tmpl = require('../utils/wechat/template')
const xml2js = require('xml2js')
const _ = require('lodash')
const crypto = require('crypto')


const pluginConfig = strapi.config.get('plugins.usersPermissions') || { branchMode: 'normal' }
const wechatConfig = strapi.config.server.wechat

function _generateWechatName () {
  return `微信用户-${Math.random().toString(36).slice(2, 6)}`
}

function _parameterizationThirdParties (thirdParties) {
  return thirdParties.map(e => {
    return {
      __component: e.__component && 'account.wechat-app',
      openid: e.openid,
      unionid: e.unionid,
      app: _.isObject(e.app) ? e.app.id : e.app,
      account: _.isObject(e.account) ? e.account.id : e.account,
    }
  })
}

// 同步更新用户三方绑定状态
// unionid相同的情况 如果没有匹配三方app则创建并附加
async function _syncUserThirdParties (user, appId, openid, unionid) {
  const iPlugin = strapi.plugins['users-permissions']
  const currentUser = _.isObject(user) ? user
    : await iPlugin.services['user'].findOne({ id: user })
  const matchThirdParty = currentUser.thirdParties.find(thirdParty => {
    return thirdParty.app === appId || thirdParty.app?.id === appId
  })
  if (!matchThirdParty) {
    return await iPlugin.services['user'].update({
      id: currentUser.id,
    }, {
      thirdParties: _parameterizationThirdParties([
        ...currentUser.thirdParties,
        {
          __component: 'account.wechat-app',
          openid: openid,
          unionid: unionid,
          app: appId,
          account: currentUser.id,
        }
      ])
    })
  }
  return currentUser
}

// 根据 _id sId 获取 App
async function _getAppById (id) {
  if (id.toString().length === 24) {
    return await strapi.query('app', 'users-permissions').findOne({ id })
  }
  return await strapi.query('app', 'users-permissions').findOne({ id })
}

function _getFullAuthData (user) {
  const sanitizeUser = {
    _id: user.id,
    id: user.id,
    username: user.username,
    description: user.description,
    phone: user.phone,
    email: user.email,
    role: user.role,
    roles: user.roles,
    avatar: user.avatar,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  }
  return {
    jwt: strapi.plugins['users-permissions'].services.jwt.issue({ id: user.id, }),
    user: sanitizeUser,
    // 兼容旧版本小程序
    account: sanitizeUser
  }
}

// 小程序登录 - unionid & openid
async function loginByMiniprogram (ctx) {
  const iPlugin = strapi.plugins['users-permissions']
  const data = ctx.request.body
  const { appId, code, userInfo } = data
  const app = await _getAppById(appId)
  if (!app) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'APP ID 错误')
  }
  const accountWechatApps = strapi.components['account.wechat-app']
  const verifyData = app.verifyData
  const { openid, unionid } = await getWechatAuthData({
    appid: verifyData.appid,
    secret: verifyData.secret,
    code: code,
  })
  if (!openid && !unionid) {
    return ctx.wrapper.error('HANDLE_ERROR', '微信认证错误')
  }
  // 依次查找 unionid openid 账号 优先使用 unionid 账号
  const unionidAccountWechatApp = unionid && await accountWechatApps.findOne({ unionid: unionid })
  const openidAccountWechatApp = openid && await accountWechatApps.findOne({ openid: openid })
  const accountWechatApp = unionidAccountWechatApp || openidAccountWechatApp
  // 如果已经存在 openid unionid 则登录成功
  if (accountWechatApp) {
    const accountUser = accountWechatApp['account']
    await _syncUserThirdParties(accountUser.id, appId, openid, unionid)
    return ctx.wrapper.succ(_getFullAuthData(accountUser))
  }
  const username = userInfo?.nickName || _generateWechatName()
  const user = await iPlugin.services['user'].createNewUser(ctx, {
    username: username,
    avatar: userInfo?.avatarUrl || '',
  }, {
    __component: 'account.wechat-app',
    app: app.id,
    openid: openid,
    unionid: unionid,
  })
  return ctx.wrapper.succ(_getFullAuthData(user))
}

// 公众号登录
async function loginByOfficialAccount (ctx) {
  const iPlugin = strapi.plugins['users-permissions']
  const data = ctx.request.body
  const { appId, code, userInfo } = data
  const app = await _getAppById(appId)
  if (!app) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'APP ID 错误')
  }
  const accountWechatApps = strapi.components['account.wechat-app']
  const verifyData = app.verifyData
  const { openid, unionid } = await getOfficialAuthData({
    appid: verifyData.appid,
    secret: verifyData.secret,
    code: code,
  })
  if (!openid && !unionid) {
    return ctx.wrapper.error('HANDLE_ERROR', '微信认证错误')
  }
  // 依次查找 unionid openid 账号 优先使用 unionid 账号
  const unionidAccountWechatApp = unionid && await accountWechatApps.findOne({ unionid: unionid })
  const openidAccountWechatApp = openid && await accountWechatApps.findOne({ openid: openid })
  const accountWechatApp = unionidAccountWechatApp || openidAccountWechatApp
  // 如果已经存在 openid unionid 则登录成功
  if (accountWechatApp) {
    const accountUser = accountWechatApp['account']
    await _syncUserThirdParties(accountUser.id, appId, openid, unionid)
    return ctx.wrapper.succ(_getFullAuthData(accountUser))
  }
  const username = userInfo?.nickName || _generateWechatName()
  const user = await iPlugin.services['user'].createNewUser(ctx, {
    username: username,
    avatar: userInfo?.avatarUrl || '',
  }, {
    __component: 'account.wechat-app',
    app: app.id,
    openid: openid,
    unionid: unionid,
  })
  return ctx.wrapper.succ(_getFullAuthData(user))
}

// 手机号登录 - 小程序手机号登录之前必读会创建 openid 账号
async function bindMiniprogramPhoneNumber (ctx) {
  const iPlugin = strapi.plugins['users-permissions']
  const data = ctx.request.body
  const user = ctx.state.user
  const { appId, code, openid } = data
  const app = await _getAppById(appId)
  if (!app) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'APP ID 错误')
  }
  // 调用微信接口获取手机号
  const verifyData = app.verifyData
  const phoneInfo = await getPhoneNumber({
    appid: verifyData.appid,
    secret: verifyData.secret,
    code: code,
  })
  const phone = phoneInfo['phoneNumber']
  // 查找手机号
  let phoneUser
  if (phone && pluginConfig.branchMode === 'domain') {
    const branch = await iPlugin.services['branch'].getCtxDomainBranch(ctx)
    phoneUser = await iPlugin.services['user'].findOne({ phone: phone, pBranch: branch.id })
  } else {
    phoneUser = await iPlugin.services['user'].findOne({ phone: phone })
  }

  // [deprecated] - 仅为兼容老版本客户端保留的逻辑
  // 查找小程序 openid
  if (openid) {
    const accountWechatApps = strapi.components['account.wechat-app']
    let openidUser
    let accountWechatApp = await accountWechatApps.findOne({ openid: openid })
    if (accountWechatApp) {
      openidUser = await iPlugin.services['user'].findOne({ id: accountWechatApp['account'].id })
    }
    // 如果对应手机号、openid分别存在两个账号则进行账号合并
    if (phoneUser && openidUser && phoneUser.id !== openidUser.id) {
      // 更新新账号
      const updateUser = await iPlugin.services['user']
        .update({ id: phoneUser.id }, {
          thirdParties: [
            ...phoneUser.thirdParties,
            ...openidUser.thirdParties
          ].map(e => {
            return {
              __component: 'account.wechat-app',
              openid: e.openid,
              unionid: e.unionid,
              app: e.app,
              account: phoneUser.id,
            }
          })
        })
      // 删除待合并账号
      await iPlugin.services['user'].delete(openidUser.id)
      return ctx.wrapper.succ(_getFullAuthData(updateUser))
    }
    // 如果openid账号已存在则绑定手机号
    if (openidUser) {
      const updateUser = await iPlugin.services['user']
        .update({ id: openidUser.id }, { phone: phone })
      return ctx.wrapper.succ(_getFullAuthData(updateUser))
    }
  }
  // 存在已登录用户
  if (user) {
    const currentUser = await iPlugin.services['user'].findOne({ id: user.id })
    if (phoneUser && phoneUser.id !== currentUser.id) {
      const updateUser = await iPlugin.services['user']
        .update({ id: phoneUser.id }, {
          phone: phone,
          thirdParties: _parameterizationThirdParties([
            ...phoneUser.thirdParties,
            ...currentUser.thirdParties
          ].map(e => {
            return {
              __component: 'account.wechat-app',
              openid: e.openid,
              unionid: e.unionid,
              app: e.app && e.app.id,
              account: phoneUser.id,
            }
          }))
        })
      // 重置已合并无效账号 - 解除关联关系
      await iPlugin.services['user'].update({ id: currentUser.id }, { thirdParties: [] })
      return ctx.wrapper.succ(_getFullAuthData(updateUser))
    }
    const updateUser = await iPlugin.services['user'].update({ id: currentUser.id }, { phone: phone })
    return ctx.wrapper.succ(_getFullAuthData(updateUser))
  }

  // 如果存在手机号则自动登录 - 应对在绑定成功的情况下二次调用绑定接口的情况
  if (phoneUser) {
    return ctx.wrapper.succ(_getFullAuthData(phoneUser))
  }
  return ctx.wrapper.error('PARAMETERS_ERROR', '未匹配此手机号账号或待绑定openid账号')
}
// 小程序url link生成
async function getMiniprogramUrlLink (ctx) {
  const { appId, path, query, env_version = 'release' } = ctx.query
  if (!path) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'path 参数缺失')
  }
  const app = await _getAppById(appId)
  if (!app) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'APP ID 错误')
  }
  const verifyData = app.verifyData
  return await genUrlLink({
    appid: verifyData.appid,
    secret: verifyData.secret,
    path: path,
    query: query,
    env_version: env_version
  })
}

async function getMiniprogramQRCode (ctx) {
  const { appId, path} = ctx.query
  if (!path) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'path 参数缺失')
  }
  const app = await _getAppById(appId)
  if (!app) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'APP ID 错误')
  }
  const verifyData = app.verifyData
  const encoding = 'binary'
  const data = await genMiniprogramQRCode({
    appid: verifyData.appid,
    secret: verifyData.secret,
    path: path,
    encoding: encoding
  })

  // 根据时间，生成文件名
  const time = new Date().getTime()
  const filename = `${time}.jpeg`
  const res = ctx.res
  res.setHeader('Content-Type', 'image/jpeg')
  res.setHeader('Content-Disposition', 'attachment; filename=' + filename)

  return Buffer.from(data, encoding)
}

async function getMiniprogramACode (ctx) {
  const { appId, path} = ctx.query
  if (!path) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'path 参数缺失')
  }
  const app = await _getAppById(appId)
  if (!app) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'APP ID 错误')
  }
  const verifyData = app.verifyData
  const encoding = 'binary'
  const data = await genMiniprogramACode({
    appid: verifyData.appid,
    secret: verifyData.secret,
    path: path,
    encoding: encoding
  })

  // 根据时间，生成文件名
  const time = new Date().getTime()
  const filename = `${time}.jpeg`
  const res = ctx.res
  res.setHeader('Content-Type', 'image/jpeg')
  res.setHeader('Content-Disposition', 'attachment; filename=' + filename)

  return Buffer.from(data, encoding)
}

// 公众号登录二维码生成
async function getOffiaccountQRCodeTicket (ctx) {
  try {
    const { appId, scene_str = undefined, scene_id = undefined, timeout = 60 } = ctx.query
    const app = await _getAppById(appId)
    if (!app) {
      return ctx.wrapper.error('PARAMETERS_ERROR', 'APP ID 错误')
    }
    const verifyData = app.verifyData
    const scene = {}
    if (scene_id) {
      scene.scene_id = scene_id
    }
    if (scene_str) {
      delete scene.scene_id
      scene.scene_str = scene_str
    }
    if (!scene_id && !scene_str) {
      return ctx.wrapper.error('PARAMETERS_ERROR', '参数值缺失')
    }
    const data = await genQRCodeTicket({
      appid: verifyData.appid,
      secret: verifyData.secret,
      scene: scene,
      timeout
    })
    data.jwt = jwtGen({
      scene: scene_id || scene_str
    }, app.tokenSecret)
    return ctx.wrapper.succ(data)
  } catch (e) {
    ctx.wrapper.error('HANDLE_ERROR', e)
  }
}

async function _subscribe (message) {
  let userID = message.FromUserName
  if (message.Event === 'subscribe') {
    return '感谢您的关注'
  } else {
    // 用户取消关注后我们不能再通过微信的接口拿到用户信息，
    // 如果要记录用户信息，需要从我们自己的用户记录里获取该信息。
    // 所以建议创建用户时除了unionid，最好把openid也保存起来。
    console.info(userID + '取关了')
  }
}

async function _xml2jsPromise (xml) {
  return new Promise((resolve, reject) => {
    xml2js.parseString(xml, (err, result) => {
      if (err) {
        reject(err)
      }
      resolve(result)
    })
  })
}

// 设置回调服务的时候，微信会验证这个请求
async function verifyOffiaccountEvent (ctx) {
  const { signature, timestamp, nonce, echostr } = ctx.request.query
  const token = wechatConfig.token
  const tmpArr = [token, timestamp, nonce]
  tmpArr.sort()
  const tmpStr = tmpArr.join('')
  const sha1Str = sha1(tmpStr)
  if (sha1Str === signature) {
    ctx.body = echostr
  } else {
    ctx.body = 'error'
  }
}

// 接收处理微信公众 XML 消息
// 参考资料：微信扫描带参数二维码事件文档
// https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Receiving_event_pushes.html
async function handleOffiaccountEvent (ctx) {
  const iPlugin = strapi.plugins['users-permissions']
  const { signature, timestamp, nonce } = ctx.request.query
  const token = wechatConfig.token
  const tmpArr = [token, timestamp, nonce]
  tmpArr.sort()
  const tmpStr = tmpArr.join('')
  const sha1Str = sha1(tmpStr)
  if (sha1Str !== signature) {
    ctx.body = 'error'
    return
  }
  const result = await _xml2jsPromise(ctx.request.body)
  // 把数组形态的xmlObject转换可读性更高的结构
  const message = fmtNormalXML(result.xml)
  const msgType = message.MsgType
  const msgEvent = message.Event
  const openid = message.FromUserName
  // scene_str
  let eventKey = message.EventKey
  let body = null
  if (msgType === 'event') {
    switch (msgEvent) {
      // 关注&取关
      case 'subscribe':
      case 'unsubscribe':
        body = await _subscribe(message)
        break
      // 关注后扫码
      case 'SCAN':
        body = '扫码成功'
        break
    }
    // 扫描带参数二维码事件
    // https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Receiving_event_pushes.html
    // eventKey Example:
    // 1.未关注状态
    //   扫码绑定账号：qrscene_62b04223b7709e11ee619a54_1658388763432
    //   扫码注册：qrscene_u82rjeows0_1658388763432
    // 2.已关注状态
    //   扫码绑定账号：62b04223b7709e11ee619a54_1658388763432
    //   扫码注册：u82rjeows0_1658388763432
    if (eventKey) {
      // 有场景值（扫了我们生成的二维码）
      if (eventKey.slice(0, 8) === 'qrscene_') {
        // 扫码并关注
        eventKey = eventKey.slice(8)
        console.info('扫码并关注了公众号')
      } else {
        console.info('扫码进入了公众号')
      }

      // 已关注
      // 1.接收到微信扫码成功通知后，如果发现 openid 已经绑定其他 User，则依然将 scene 字段更新到此 User（from key）
      // 2.更新 scene 后 checkLogin 会匹配到账号，但可以发现账号未绑定 thirdParties，此时提示账号已绑定；
      // 3.提示后，是否清除 scene 都应该都没有影响；
      // 场景值关联用户
      const sceneUserId = eventKey.split('_')[1]
      const sceneUser = sceneUserId.length === 24 ? await iPlugin.services['user'].findOne({ id: sceneUserId }) : null
      // 获取 unionid
      const appId = eventKey.split('_')[0]
      const app = await _getAppById(appId)
      const verifyData = app.verifyData
      const offiaccountUserInfo = await getOffiaccountUserInfo({
        appid: verifyData.appid,
        secret: verifyData.secret,
        openid: openid,
      })
      const unionid = offiaccountUserInfo.unionid
      // 扫码 openid 关联三方
      const accountWechatApps = strapi.components['account.wechat-app']
      const eventWechatApp = unionid
        ? await accountWechatApps.findOne({ unionid: unionid })
        : await accountWechatApps.findOne({ openid: openid })

      // 用户扫码邀请绑定流程 - 存在场景值关联用户
      if (sceneUser) {
        // 存在匹配三方账号进行直接登录或合并账号与租户操作
        if (eventWechatApp) {
          const eventWechatAppUser = await iPlugin.services['user'].findOne({ id: eventWechatApp.account.id })
          // 如果场景值用户与匹配三方用户不一致则合并账号与租户
          if (eventWechatAppUser.id !== sceneUser.id) {
            // 匹配三方用户绑定新租户
            const defaultRole = await iPlugin.services['user'].getDefaultRole()
            const role = sceneUser.role?.id || defaultRole.id
            const roles = sceneUser.roles?.map(e => e.id) || [role]
            await iPlugin.services['user'].updateOne(eventWechatAppUser.id, {
              role: role,
              roles: roles,
              pBranch: sceneUser.pBranch.id,
              scene: eventKey,
              pBranches: [
                ...eventWechatAppUser.pBranches?.map(e => e.id),
                sceneUser.pBranch.id
              ],
              pBranchConfigs: [
                ...eventWechatAppUser.pBranchConfigs,
                {
                  branchId: sceneUser.pBranch.id,
                  // 同步合并用户数据
                  role: role,
                  roles: roles,
                  ..._.pick(sceneUser.pBranchConfigs[0], [
                    'blocked',
                    'username', 'phone', 'email'
                  ])
                }
              ]
            })
            // 删除旧用户
            await iPlugin.services['user'].delete(sceneUser.id)
          }
          // 如果场景值用户与匹配三方用户一致则直接登录
          else {
            await iPlugin.services['user'].updateOne(sceneUser.id, {
              scene: eventKey,
            })
          }
        }
        // 不存在匹配三方账号则进行绑定操作=
        else {
          await iPlugin.services['user'].update({
            id: sceneUser.id,
          }, {
            scene: eventKey,
            thirdParties: [
              {
                __component: 'account.wechat-app',
                openid: openid,
                unionid: unionid,
                app: appId,
                account: sceneUser.id,
              },
            ],
          })
        }
      }
      // 用户扫码邀请绑定流程 - 多次扫码同一个链接情况且 sceneUser 已合并消失
      if (!sceneUser && sceneUserId.length === 24) {
        console.info(`邀请连接用户重复扫码`, sceneUserId)
      }
      // 用户扫码登录流程
      if (!sceneUser && sceneUserId.length !== 24) {
        // eventKey中用户id查不到(判断系统中三方登录组件中存在此用户没有，没有就新建，有就更新scene)
        if (eventWechatApp) {
          await _syncUserThirdParties(eventWechatApp.account.id, appId, openid, unionid)
          await iPlugin.services['user'].update({
            id: eventWechatApp.account.id,
          }, {
            scene: eventKey
          })
        } else {
          const username = _generateWechatName()
          await iPlugin.services['user'].createNewUser(ctx, {
            provider: app.name,
            username: username,
            scene: eventKey
          }, {
            __component: 'account.wechat-app',
            openid: openid,
            unionid: unionid,
            app: appId
          })
        }
      }
    }
  }
  ctx.type = 'application/xml'
  ctx.body = tmpl(body || ctx.body, message)
}

// 公众号二维码登录状态检查
async function checkOffiaccountLogin (ctx) {
  const iPlugin = strapi.plugins['users-permissions']
  try {
    const { appId, token } = ctx.request.body
    const app = await _getAppById(appId)
    if (!app) {
      return ctx.wrapper.error('PARAMETERS_ERROR', 'APP ID 错误')
    }
    const decodedToken = await jwtVerify(token, app.tokenSecret)
    const scene = decodedToken.data.scene
    const sceneUserId = scene.split('_')[1]
    if (sceneUserId.length === 24) {
      const sceneUserCount = await iPlugin.services['user'].count({ id: sceneUserId })
      if (sceneUserCount <= 0) {
        return ctx.wrapper.succ({
          code: 5,
          msg: '该邀请链接已失效！'
        })
      }
    }
    let user = await iPlugin.services['user'].findOne({ scene })
    if (user) {
      if (!user.thirdParties || user.thirdParties.length === 0) {
        return ctx.wrapper.succ({
          code: 5,
          msg: '该微信帐号已绑定其他账号，请联系管理员解绑后再进行操作！'
        })
      } else {
        return ctx.wrapper.succ(_getFullAuthData(user))
      }
    } else {
      return ctx.wrapper.succ(null)
    }
  } catch (err) {
    return ctx.wrapper.error('HANDLE_ERROR', err)
  }
}

// 公众号用户信息获取 - 根据 openId 获取 unionId
async function getUserOffiaccountInfo (ctx) {
  const { appId, userId } = ctx.request.body
  const app = await _getAppById(appId)
  const verifyData = app.verifyData
  if (!verifyData) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'APP ID 错误')
  }
  const user = await strapi.plugins['users-permissions'].services['user'].findOne({ id: userId })
  return await getOffiaccountUserInfo({
    appid: verifyData.appid,
    secret: verifyData.secret,
    openid: user.thirdParties[0].openid,
  })
}

async function getOffiaccountSignature (ctx) {
  const { appId, url } = ctx.request.query

  const app = await _getAppById(appId)
  const verifyData = app.verifyData
  if (!verifyData) {
    return ctx.wrapper.error('PARAMETERS_ERROR', 'APP ID 错误')
  }

  let ticket = await getOffiaccountJSTicket({
    appid: verifyData.appid,
    secret: verifyData.secret,
  })

  if (!ticket) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '获取 ticket 失败')
  }

  let timestamp = Date.now().toString().slice(0, 10);
  let nonceStr = genRandomString(32)
  let signature = signParams({
    noncestr: nonceStr,
    jsapi_ticket: ticket,
    timestamp: timestamp,
    url: url
  });
  return ctx.wrapper.succ({
    appId: verifyData.appid,
    timestamp: timestamp,
    nonceStr: nonceStr,
    signature: signature
  })
}

function signParams(params) {
  let keys = Object.keys(params).sort()
  const str = keys.map(key => `${key}=${params[key]}`).join('&')
  return crypto.createHash('sha1').update(str).digest('hex');
}

module.exports = {
  loginByMiniprogram,
  loginByOfficialAccount,
  bindMiniprogramPhoneNumber,
  getMiniprogramUrlLink,
  getMiniprogramQRCode,
  getMiniprogramACode,
  getOffiaccountQRCodeTicket,
  checkOffiaccountLogin,
  verifyOffiaccountEvent,
  handleOffiaccountEvent,
  getUserOffiaccountInfo,
  getOffiaccountSignature
}
